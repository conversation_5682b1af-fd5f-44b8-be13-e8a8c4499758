<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Albaraka - Travel Booking</title>
    {{-- <link href="https://cdn.jsdelivr.net/npm/daisyui@4.6.0/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script> --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">




    <script src="https://unpkg.com/lucide@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>



    <!-- ✅ Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
        integrity="sha512-..." crossorigin="anonymous" referrerpolicy="no-referrer" />


</head>

<body class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-8">
                    <h1 class="text-2xl font-bold text-orange-600">Albaraka</h1>
                    <nav class="hidden md:flex space-x-6">
                        <a href="#" class="flex items-center text-gray-600 hover:text-orange-600">
                            <i data-lucide="plane" class="w-4 h-4 mr-1"></i> Flights
                        </a>
                        <a href="#" class="flex items-center text-gray-600 hover:text-orange-600">
                            <i data-lucide="hotel" class="w-4 h-4 mr-1"></i> Hotels
                        </a>
                        <a href="#" class="flex items-center text-gray-600 hover:text-orange-600">
                            <i data-lucide="car" class="w-4 h-4 mr-1"></i> Car Rental
                        </a>
                    </nav>
                </div>
                <a href="#" class="bg-green-600 text-white px-4 py-2 hover:bg-orange-700">
                    Sign In
                </a>
            </div>
        </div>
    </header>
    <div class="container mx-auto px-4 py-8">
        <div class="passenger-info bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-2xl font-bold text-gray-800 mb-6">Passenger Information</h3>
            <form id="passengerForm">
                @foreach (range(1, $passengerCount) as $index)
                    <div class="passenger-card bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
                        <h4 class="text-lg font-semibold text-gray-700 mb-4">Passenger {{ $index }}</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                            <!-- Title -->
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                                <select name="passenger_info[{{ $index - 1 }}][prefix]" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                    <option value="Mr">Mr</option>
                                    <option value="Mrs">Mrs</option>
                                    <option value="Ms">Ms</option>
                                </select>
                            </div>

                            <!-- First Name -->
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                <input type="text" name="passenger_info[{{ $index - 1 }}][first_name]" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>

                            <!-- Last Name -->
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                <input type="text" name="passenger_info[{{ $index - 1 }}][last_name]" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>

                            <!-- Gender -->
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                                <select name="passenger_info[{{ $index - 1 }}][gender]" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                    <option value="M">Male</option>
                                    <option value="F">Female</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <!-- Phone -->
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                                <input type="tel" name="passenger_info[{{ $index - 1 }}][phone]" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    placeholder="+8801XXXXXXXXX">
                            </div>

                            <!-- Email -->
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                <input type="email" name="passenger_info[{{ $index - 1 }}][email]" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <input type="text" name="passenger_info[{{ $index - 1 }}][address][street]"
                                    placeholder="Street" required
                                    class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <input type="text" name="passenger_info[{{ $index - 1 }}][address][city]"
                                    placeholder="City" required
                                    class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    value="Dhaka">
                                <input type="text" name="passenger_info[{{ $index - 1 }}][address][country]"
                                    placeholder="Country" required
                                    class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                    value="Bangladesh">
                            </div>
                        </div>

                        <!-- Passport Information (Required for International Flights) -->
                        <div class="passport-info bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <h5 class="text-md font-medium text-blue-800 mb-3">Passport Information</h5>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div class="form-group">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Passport Number</label>
                                    <input type="text" name="passenger_info[{{ $index - 1 }}][passport][number]"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div class="form-group">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Issue Date</label>
                                    <input type="date"
                                        name="passenger_info[{{ $index - 1 }}][passport][issue_date]" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div class="form-group">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                                    <input type="date"
                                        name="passenger_info[{{ $index - 1 }}][passport][expiry_date]" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div class="form-group">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Nationality</label>
                                    <input type="text"
                                        name="passenger_info[{{ $index - 1 }}][passport][nationality]" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                                        value="Bangladesh">
                                </div>
                            </div>
                        </div>

                        <input type="hidden" name="passenger_info[{{ $index - 1 }}][type]" value="ADT">
                    </div>
                @endforeach
            </form>
        </div>

        <div class="booking-actions bg-white rounded-lg shadow-md p-6">
            <form id="bookingForm" action="{{ route('flight.booking.initiate') }}" method="POST">
                @csrf
                <input type="hidden" name="solution_data" value="{{ json_encode($solution) }}">
                <input type="hidden" name="passenger_info" id="passengerData">
                <button type="button" id="bookNowButton"
                    class="btn-book-now w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-3 px-4 rounded-md transition duration-300 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z">
                        </path>
                    </svg>
                    Book Now
                </button>
            </form>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passengerForm = document.getElementById('passengerForm');
            const bookingForm = document.getElementById('bookingForm');
            const bookNowButton = document.getElementById('bookNowButton');
            const passengerDataField = document.getElementById('passengerData');
    
            // Handle Book Now button click
            bookNowButton.addEventListener('click', function() {

               // alert('Please fill in all passenger details');
                // First collect all passenger data
                const formData = new FormData(passengerForm);
                const passengerData = {};
    
                formData.forEach((value, key) => {
                    // Convert form data to structured object
                    const keys = key.split(/\[|\]\[|\]/).filter(k => k);
                    let obj = passengerData;
    
                    for (let i = 0; i < keys.length - 1; i++) {
                        const currentKey = keys[i];
                        if (!obj[currentKey]) {
                            obj[currentKey] = isNaN(keys[i + 1]) ? {} : [];
                        }
                        obj = obj[currentKey];
                    }
    
                    obj[keys[keys.length - 1]] = value;
                });
    
                // Validate if all required fields are filled
                if (!passengerData.passenger_info || Object.keys(passengerData.passenger_info).length === 0) {
                    alert('Please fill in all passenger details');
                    return;
                }
    
                // Check individual fields (example check)
                const firstPassenger = passengerData.passenger_info[0];
                if (!firstPassenger.first_name || !firstPassenger.last_name || !firstPassenger.phone) {
                    alert('Please fill in all required passenger details');
                    return;
                }
    
                // Set the passenger data in the hidden field
                passengerDataField.value = JSON.stringify(passengerData.passenger_info);
    
                // Submit the booking form
                bookingForm.submit();
            });
    
            // Optional: Also handle passenger form submission if needed
            passengerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                bookNowButton.click(); // Trigger the same logic as the Book Now button
            });
        });
    </script>