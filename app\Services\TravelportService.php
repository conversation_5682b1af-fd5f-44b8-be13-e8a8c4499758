<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use SimpleXMLElement;

class TravelportService
{

    protected $client;
    protected $username = 'uAPI7117508323-d2ae3bd0';
    protected $uni_username = 'Universal API/uAPI7117508323-d2ae3bd0';
    protected $password = 'B%g9+a6HX$'; // Replace with your actual password
    protected $targetBranch = 'P7230979';
    protected $pcc = '8BO1';

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://apac.universal-api.pp.travelport.com/B2BGateway/connect/uAPI/',
            'auth' => [$this->uni_username, $this->password],
            'headers' => [
                'Content-Type' => 'text/xml; charset=UTF-8',
                'Accept' => 'application/xml',
                'SOAPAction' => 'AirService' // Required by Travelport
            ],
            'timeout' => 30, // Request timeout in seconds
            'connect_timeout' => 10, // Connection timeout
            'verify' => true // Enable SSL verification
        ]);
    }

    public function searchFlights($origin, $destination, $departureDate, $cabinClass = null, $trip_type = null, $returnDate = null, $adults = 1, $children = 0, $infants = 0)
    {
        // Validate the input parameters
        if (empty($origin) || empty($destination) || empty($departureDate)) {
            throw new \InvalidArgumentException('Origin, destination, and departure date are required.');
        }

        // Set default values for optional parameters
        $cabinClass = $cabinClass ?? 'Economy';
        $trip_type = $trip_type ?? 'oneway'; // Default to one-way trip if not provided
        $returnDate = $returnDate ?? null;
        $adults = $adults ?? 1; // Default to 1 adult if not provided
        $children = $children ?? 0; // Default to 0 children if not provided
        $infants = $infants ?? 0; // Default to 0 infants if not provided



        $xml = view('xml.air_search', [
            'origin' => $origin,
            'destination' => $destination,
            'departureDate' => $departureDate,
            'targetBranch' => $this->targetBranch,
            'pcc' => $this->pcc,
            'username' => $this->username,
            'cabinClass' => $cabinClass,
            'trip_type' => $trip_type,
            'returnDate' => $returnDate,
            'adults' => $adults,
            'children' => $children,
            'infants' => $infants
        ])->render();

        try {

            $response = $this->client->post('AirService', ['body' => $xml]);
            return $response->getBody()->getContents(); // Parse with SimpleXML or DOM later
        } catch (\Exception $e) {
            Log::error('XML Generation Error: ' . $e->getMessage());
            throw new \RuntimeException('Failed to generate XML request');
        }

        //Log::info('Raw API Response:', ['response' =>$response->getBody()]);

        return $response->getBody()->getContents(); // Parse with SimpleXML or DOM later
    }

    public function getAirPrice($solution)
    {
        // Validate required data
        if (!isset($solution['solutionKey'], $solution['segments'][0])) {
            throw new \InvalidArgumentException('Invalid solution data provided');
        }
        // Prepare the XML using Blade template
        // Log::info('Solution Data:', $solution);
        $xml = view('xml.AirPriceRequest', [
            'targetBranch' => $this->targetBranch,
            'pcc' => $this->pcc,
            'username' => $this->username,
            'password' => $this->password,
            'uni_username' => $this->uni_username,
            'solution' => $this->normalizeSolutionData($solution),
        ])->render();

        // return $xml; // For debugging purposes

        try {
            $response = $this->client->post('AirService', [
                'body' => $xml,
                'http_errors' => false // To handle 4xx/5xx as responses not exceptions
            ]);
            return $response->getBody()->getContents(); // Parse with SimpleXML or DOM later
        } catch (\Exception $e) {
            Log::error('XML Generation Error: ' . $e->getMessage());
            throw new \RuntimeException('Failed to generate XML request');
        }
    }

    public function generateAirCreateReservationRequest($solution, $passengerInfo)
    {
        // Validate required data
        if (!isset($solution['solutionKey'], $solution['segments'][0])) {
            throw new \InvalidArgumentException('Invalid solution data provided');
        }

        // Prepare the XML using Blade template
        $xml = view('xml.air_create_reservation', [
            'targetBranch' => $this->targetBranch,
            'username' => $this->username,
            'password' => $this->password,
            'solution' => $this->normalizeSolutionData($solution),
            'passengerInfo' => $this->normalizePassengerData($passengerInfo)
        ])->render();


        // return $xml; // For debugging purposes

        try {
            $response = $this->client->post('AirService', [
                'body' => $xml,
                'http_errors' => false // To handle 4xx/5xx as responses not exceptions
            ]);
            return $response->getBody()->getContents(); // Parse with SimpleXML or DOM later
        } catch (\Exception $e) {
            Log::error('XML Generation Error: ' . $e->getMessage());
            throw new \RuntimeException('Failed to generate XML request');
        }

        return $response;
    }

    protected function normalizeSolutionData($solution)
    {
        // Convert BDT amounts to numbers only
        $solution['taxAmount'] = str_replace('BDT', '', $solution['taxAmount']);
        $solution['approximateTotalPrice'] = str_replace('BDT', '', $solution['approximateTotalPrice']);

        // Ensure all segments have required keys
        foreach ($solution['segments'] as &$segment) {
            $segment['key'] = $segment['key'] ?? uniqid('seg-');
            $segment['flightDetailsRef'] = $segment['flightDetailsRef'] ?? uniqid('flt-');
        }

        return $solution;
    }

    protected function normalizePassengerData($passengers)
    {


        // Add default passport info if missing
        foreach ($passengers as &$passenger) { // Use reference &
            $passenger['passport'] = $passenger['passport'] ?? [
                'number' => 'N/A',
                'issue_date' => '2020-01-01',
                'expiry_date' => '2030-01-01'
            ];

            // Log::info('passengers Request:', $passenger['passport']);
        }

        return $passengers;
    }


    public function getBookingDetails($data)
    {
        $xml = view('xml.booking_details', [
            'targetBranch' => $this->targetBranch,
            'pcc' => $this->pcc,
            'username' => $this->username,
            'data' => $data
        ])->render();

        $response = $this->client->post('AirPriceRQ', ['body' => $xml]);

        //Log::info('Raw API Response:', ['response' =>$response->getBody()]);

        return $response->getBody()->getContents(); // Parse with SimpleXML or DOM later
    }

    public function parseAirPricingSolution($xmlString)
    {

        $xml = new SimpleXMLElement($xmlString);

        // Register namespaces
        $xml->registerXPathNamespace('SOAP', 'http://schemas.xmlsoap.org/soap/envelope/');
        $xml->registerXPathNamespace('air', 'http://www.travelport.com/schema/air_v52_0');

        $result = [
            'segments' => [],
            'FareInfo' => [],
            'pricingSolutions' => [],
            'fareRules' => []
        ];

        // Parse all fare rules first
        foreach ($xml->xpath('//air:FareRule') as $fareRule) {
            $attrs = $fareRule->attributes();
            $fareInfoRef = (string)$attrs->FareInfoRef;

            $ruleData = [
                'ruleNumber' => (string)$attrs->RuleNumber,
                'source' => (string)$attrs->Source,
                'tariffNumber' => (string)$attrs->TariffNumber,
                'categories' => []
            ];

            foreach ($fareRule->xpath('.//air:FareRuleLong') as $ruleLong) {
                $category = (string)$ruleLong->attributes()->Category;
                $type = (string)$ruleLong->attributes()->Type;
                $content = trim((string)$ruleLong);

                $ruleData['categories'][$category] = [
                    'type' => $type,
                    'content' => $content
                ];
            }

            $result['fareRules'][$fareInfoRef] = $ruleData;
        }

        // Parse all segments
        foreach ($xml->xpath('//air:AirSegment') as $segment) {
            $attrs = $segment->attributes();
            $departure = new \DateTime((string)$attrs->DepartureTime);
            $arrival = new \DateTime((string)$attrs->ArrivalTime);

            $result['segments'][(string)$attrs->Key] = [
                'departure' => $departure->format('Y-m-d H:i:s'),
                'arrival' => $arrival->format('Y-m-d H:i:s'),
                'flightNumber' => (string)$attrs->FlightNumber,
                'carrier' => (string)$attrs->Carrier,
                'origin' => (string)$attrs->Origin,
                'destination' => (string)$attrs->Destination,
                'duration' => (string)$attrs->FlightTime,
                'aircraft' => (string)$attrs->Equipment,
                'stopover' => (string)$attrs->ChangeOfPlane === 'true',
                'classOfService' => (string)$attrs->ClassOfService,
                'distance' => (string)$attrs->Distance,
                'codeshare' => (string)$segment->xpath('air:CodeshareInfo')[0] ?? null,
                'meals' => (string)$segment->xpath('air:FlightDetails/air:Meals')[0] ?? null,
                'services' => (string)$segment->xpath('air:FlightDetails/air:InFlightServices')[0] ?? null
            ];
        }

        // Parse all fare information
        foreach ($xml->xpath('//air:FareInfo') as $fareInfo) {
            $attrs = $fareInfo->attributes();
            $key = (string)$attrs->Key;

            $result['FareInfo'][$key] = [
                'fareBasis' => (string)$attrs->FareBasis,
                'passengerType' => (string)$attrs->PassengerTypeCode,
                'origin' => (string)$attrs->Origin,
                'destination' => (string)$attrs->Destination,
                'amount' => (string)$attrs->Amount,
                'taxAmount' => (string)$attrs->TaxAmount,
                'validFrom' => (string)$attrs->NotValidBefore,
                'validTo' => (string)$attrs->NotValidAfter,
                'negotiatedFare' => (string)$attrs->NegotiatedFare === 'true',
                'ticketingCode' => (string)$fareInfo->xpath('air:TicketingCode/@Value')[0] ?? null,
                'endorsements' => array_map(function ($e) {
                    return (string)$e->attributes()->Value;
                }, $fareInfo->xpath('.//common_v52_0:Endorsement')),
                'fareRules' => (string)$fareInfo->xpath('air:FareRuleKey')[0] ?? null,
                'ruleDetails' => $result['fareRules'][$key] ?? null,
                'brandInfo' => ($brand = $fareInfo->xpath('.//air:Brand')) ? [
                    'brandID' => (string)$brand[0]->attributes()->BrandID,
                    'upSellBrandID' => (string)$brand[0]->attributes()->UpSellBrandID,
                    'brandTier' => (string)$brand[0]->attributes()->BrandTier
                ] : null
            ];
        }

        // Parse all pricing solutions
        foreach ($xml->xpath('//air:AirPricingSolution') as $solution) {
            $solutionAttrs = $solution->attributes();

            // Get segment references for this solution
            $solutionSegments = [];
            foreach ($solution->xpath('.//air:AirSegmentRef') as $ref) {
                $solutionSegments[] = (string)$ref->attributes()->Key;
            }

            // Process each pricing info in the solution
            foreach ($solution->xpath('.//air:AirPricingInfo') as $pricingInfo) {
                $pricingInfoAttrs = $pricingInfo->attributes();

                // Get fare information for this pricing info
                $solutionFareinfo = [];
                foreach ($pricingInfo->xpath('.//air:FareInfo') as $fareInfo) {
                    $fareKey = (string)$fareInfo->attributes()->Key;
                    if (isset($result['FareInfo'][$fareKey])) {
                        $solutionFareinfo[$fareKey] = $result['FareInfo'][$fareKey];
                    }
                }

                // Process tax information
                $taxes = [];
                foreach ($pricingInfo->xpath('.//air:TaxInfo') as $tax) {
                    $taxAttrs = $tax->attributes();
                    $taxDetails = [
                        'category' => (string)$taxAttrs->Category,
                        'amount' => (string)$taxAttrs->Amount
                    ];

                    if ($details = $tax->xpath('.//common_v52_0:TaxDetail')) {
                        foreach ($details as $detail) {
                            $taxDetails['details'][] = [
                                'amount' => (string)$detail->attributes()->Amount,
                                'origin' => (string)$detail->attributes()->OriginAirport ?? null
                            ];
                        }
                    }

                    $taxes[] = $taxDetails;
                }

                // Process penalties
                $changePenalty = $pricingInfo->xpath('.//air:ChangePenalty');
                $cancelPenalty = $pricingInfo->xpath('.//air:CancelPenalty');

                // Get first booking info
                $bookingInfo = $pricingInfo->xpath('.//air:BookingInfo')[0] ?? null;
                $bookingAttrs = $bookingInfo ? $bookingInfo->attributes() : null;

                $result['pricingSolutions'][] = [
                    'totalPrice' => (string)$solutionAttrs->TotalPrice,
                    'basePrice' => (string)$solutionAttrs->BasePrice,
                    'approximateTotalPrice' => (string)$solutionAttrs->ApproximateTotalPrice,
                    'approximateBasePrice' => (string)$solutionAttrs->ApproximateBasePrice,
                    'refundable' => (string)$pricingInfoAttrs->Refundable,
                    'taxesPriceInfo' => (string)$pricingInfoAttrs->Taxes,
                    'platingCarrier' => (string)$pricingInfoAttrs->PlatingCarrier,
                    'ticketdate' => (string)$pricingInfoAttrs->LatestTicketingTime,
                    'taxes' => $taxes,
                    'taxAmount' => (string)$solutionAttrs->Taxes,
                    'currency' => substr((string)$solutionAttrs->TotalPrice, 0, 3),
                    'segments' => $solutionSegments,
                    'fareInfo' => $solutionFareinfo,
                    'changePenalty' => $changePenalty ? [
                        'type' => (string)$changePenalty[0]->attributes()->PenaltyApplies,
                        'amount' => isset($changePenalty[0]->Amount) ? (string)$changePenalty[0]->Amount : null,
                        'percentage' => isset($changePenalty[0]->Percentage) ? (string)$changePenalty[0]->Percentage : null
                    ] : null,
                    'cancelPenalty' => $cancelPenalty ? [
                        'type' => (string)$cancelPenalty[0]->attributes()->PenaltyApplies,
                        'amount' => isset($cancelPenalty[0]->Amount) ? (string)$cancelPenalty[0]->Amount : null,
                        'percentage' => isset($cancelPenalty[0]->Percentage) ? (string)$cancelPenalty[0]->Percentage : null
                    ] : null,
                    'platingCarrier' => (string)$pricingInfoAttrs->PlatingCarrier,
                    'airline' => $airline[(string)$pricingInfoAttrs->PlatingCarrier] ?? (string)$pricingInfoAttrs->PlatingCarrier,
                    'providerCode' => (string)$pricingInfoAttrs->ProviderCode,
                    'ticketingTime' => isset($pricingInfoAttrs->LatestTicketingTime) ?
                        (new \DateTime((string)$pricingInfoAttrs->LatestTicketingTime))->format('M d, Y h:i A') : null,
                    'fareBasis' => $this->extractFareBasis($solution),
                    'bookingClass' => $bookingAttrs ? (string)$bookingAttrs->BookingCode : null,
                    'seatsAvailable' => $bookingAttrs && isset($bookingAttrs->BookingCount) ? (int)$bookingAttrs->BookingCount : null,
                    'cabinClass' => $bookingAttrs ? (string)$bookingAttrs->CabinClass : null,
                    'FareInfoRef' => $bookingAttrs ? (string)$bookingAttrs->FareInfoRef : null,
                    'SegmentRef' => $bookingAttrs ? (string)$bookingAttrs->SegmentRef : null,
                    'solutionKey' => (string)$solutionAttrs->Key
                ];
            }
        }

        return $result;
    }


    // Helper function to format travel time
    private function formatTravelTime($minutes)
    {
        $hours = floor($minutes / 60);
        $mins = $minutes % 60;
        return sprintf('%dh %02dm', $hours, $mins);
    }

    private function extractFareBasis($solution)
    {
        $fareBases = [];
        foreach ($solution->xpath('.//air:FareInfo') as $fareInfo) {
            $fareBases[] = (string)$fareInfo->attributes()->FareBasis;
        }
        return array_unique($fareBases);
    }
}
