<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">

        <air:LowFareSearchReq xmlns:air="http://www.travelport.com/schema/air_v51_0"
            xmlns:com="http://www.travelport.com/schema/common_v51_0" TargetBranch="{{ $targetBranch }}"
            AuthorizedBy="{{ $username }}"  TraceId="{{ uniqid('bd-b2b-search-') }}"  SolutionResult="1">
            <com:BillingPointOfSaleInfo OriginApplication="UAPI" />
            <air:SearchAirLeg>
                <air:SearchOrigin>
                    <com:CityOrAirport Code="{{ $origin }}" />
                </air:SearchOrigin>
                <air:SearchDestination>
                    <com:CityOrAirport Code="{{ $destination }}" />
                </air:SearchDestination>
                <air:SearchDepTime PreferredTime="{{ $departureDate }}" />
                <air:AirLegModifiers>
                    <air:PreferredCabins>
                        <com:CabinClass Type="{{ $cabinClass }}" />
                    </air:PreferredCabins>
                    <!-- <air:NonStopOnly>true</air:NonStopOnly> -->
                    <!-- <air:MaxStops>1</air:MaxStops> -->
                    
                </air:AirLegModifiers>
            </air:SearchAirLeg>

            @if ($returnDate)
                <air:SearchAirLeg>
                    <air:SearchOrigin>
                        <com:CityOrAirport Code="{{ $destination }}" />
                    </air:SearchOrigin>
                    <air:SearchDestination>
                        <com:CityOrAirport Code="{{ $origin }}" />
                    </air:SearchDestination>
                    <air:SearchDepTime PreferredTime="{{ $returnDate }}" />
                    <air:AirLegModifiers>
                        <air:PreferredCabins>
                            <com:CabinClass Type="{{ $cabinClass }}" />
                        </air:PreferredCabins>
                    </air:AirLegModifiers>

                </air:SearchAirLeg>
            @endif

            <air:AirSearchModifiers MaxSolutions="500">
                <air:PreferredProviders>
                    <com:Provider Code="1G" />
                </air:PreferredProviders>
            </air:AirSearchModifiers>

            <!-- Passenger information -->
            <com:SearchPassenger Code="ADT" Quantity="{{ $adults }}" />
            <com:SearchPassenger Code="CNN" Quantity="{{ $children }}" />
            <com:SearchPassenger Code="INF" Quantity="{{ $infants }}" />

            <!-- OR for infant with seat -->
            <!-- <com:SearchPassenger Code="INS" Quantity="1"/> -->


            {{-- <com:SearchPassenger 
            ="AA0" Code="ADT" Age="40" /> --}}


            <air:AirPricingModifiers AccountCodeFaresOnly="false" ETicketability="Required" FaresIndicator="AllFares" />
        </air:LowFareSearchReq>
    </s:Body>
</s:Envelope>
