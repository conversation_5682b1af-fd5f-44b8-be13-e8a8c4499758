<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agents', function (Blueprint $table) {
            $table->id();
            $table->string('name')->index();
            $table->string('mobile')->index();
            $table->string('email')->index();
            $table->string('pincode')->default('1234');
            $table->decimal('balance',52,2)->default(0.00);
            $table->decimal('credit',52,2)->default(0.00);
            $table->decimal('limit',52 ,2)->default(0.00);
            $table->decimal('bonus',52,2)->default(0.00);
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('company_name')->nullable()->index();
            $table->string('company_address')->nullable();
            $table->enum('type', ['b2c', 'b2c', 'b2e','api', 'others'])->default('b2c')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active')->nullable();
            $table->longText('ext_1')->nullable();
            $table->longText('ext_2')->nullable();
            $table->longText('ext_3')->nullable();
            $table->longText('ext_4')->nullable();
            $table->longText('ext_5')->nullable();
            $table->longText('ext_6')->nullable();
            $table->longText('ext_7')->nullable();
            $table->enum('db_status', ['live', 'deleted'])->default('live')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agents');
    }
};
