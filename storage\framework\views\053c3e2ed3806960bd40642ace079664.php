<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Search Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <div class="bg-red-600 text-white p-2 rounded-full">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-gray-900">DAC - DXB | Total 40 Flights</h1>
                        <p class="text-sm text-gray-600">31 May 2025 | 1 Travelers</p>
                    </div>
                </div>
                <button class="bg-red-600 text-white px-4 py-2 rounded font-medium hover:bg-red-700">
                    MODIFY SEARCH
                </button>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 py-6">
        <div class="flex gap-6">
            <!-- Filters Sidebar -->
            <div class="w-80 flex-shrink-0">
                <div class="bg-white rounded-lg shadow-sm p-4">
                    <!-- Filter Header -->
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="font-semibold text-gray-900">FILTER</h2>
                        <button class="text-red-600 text-sm font-medium">RESET</button>
                    </div>

                    <!-- Sort Options -->
                    <div class="mb-6">
                        <div class="flex gap-2">
                            <button class="bg-blue-900 text-white px-4 py-2 rounded text-sm font-medium">CHEAPEST</button>
                            <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded text-sm font-medium">FASTEST</button>
                        </div>
                    </div>

                    <!-- Price Range -->
                    <div class="mb-6">
                        <h3 class="font-medium text-gray-900 mb-3 flex items-center">
                            Price Range
                            <svg class="w-4 h-4 ml-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                            </svg>
                        </h3>
                        <div class="space-y-3">
                            <div class="flex justify-between text-sm text-gray-600">
                                <span>৳ 32,259</span>
                                <span>৳ 587,108</span>
                            </div>
                            <div class="relative">
                                <input type="range" min="32259" max="587108" value="32259" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                <div class="flex justify-between mt-2">
                                    <div class="w-3 h-3 bg-red-600 rounded-full"></div>
                                    <div class="w-3 h-3 bg-red-600 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fare Type -->
                    <div class="mb-6">
                        <h3 class="font-medium text-gray-900 mb-3 flex items-center">
                            Fare Type
                            <svg class="w-4 h-4 ml-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                            </svg>
                        </h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700">Refundable</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700">Non Refundable</span>
                            </label>
                        </div>
                    </div>

                    <!-- Stops -->
                    <div class="mb-6">
                        <h3 class="font-medium text-gray-900 mb-3 flex items-center">
                            Stops
                            <svg class="w-4 h-4 ml-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                            </svg>
                        </h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700">Non Stop</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700">One Stop</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700">One Plus Stops</span>
                            </label>
                        </div>
                    </div>

                    <!-- Departure Times -->
                    <div class="mb-6">
                        <h3 class="font-medium text-gray-900 mb-3 flex items-center">
                            Departure Times
                            <svg class="w-4 h-4 ml-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"/>
                            </svg>
                        </h3>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707"/>
                                    </svg>
                                    00:00 - 05:59
                                </span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707"/>
                                    </svg>
                                    06:00 - 11:59
                                </span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-red-600 focus:ring-red-500">
                                <span class="ml-2 text-sm text-gray-700 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707"/>
                                    </svg>
                                    12:00 - 17:59
                                </span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Flight Results -->
            <div class="flex-1">
                <!-- Airlines Filter -->
                <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
                    <div class="flex items-center gap-4 overflow-x-auto">
                        <div class="flex items-center gap-2 whitespace-nowrap">
                            <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                <span class="text-xs font-bold text-blue-600">BS</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium">BS</div>
                                <div class="text-xs text-gray-500">BDT 31,492</div>
                            </div>
                        </div>
                        <div class="flex items-center gap-2 whitespace-nowrap">
                            <div class="w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center">
                                <span class="text-xs font-bold text-orange-600">UL</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium">UL</div>
                                <div class="text-xs text-gray-500">BDT 32,022</div>
                            </div>
                        </div>
                        <div class="flex items-center gap-2 whitespace-nowrap">
                            <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                                <span class="text-xs font-bold text-green-600">FZ</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium">FZ</div>
                                <div class="text-xs text-gray-500">BDT 42,011</div>
                            </div>
                        </div>
                        <div class="flex items-center gap-2 whitespace-nowrap">
                            <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                                <span class="text-xs font-bold text-purple-600">6E</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium">6E</div>
                                <div class="text-xs text-gray-500">BDT 42,158</div>
                            </div>
                        </div>
                        <div class="flex items-center gap-2 whitespace-nowrap">
                            <div class="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center">
                                <span class="text-xs font-bold text-yellow-600">WY</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium">WY</div>
                                <div class="text-xs text-gray-500">BDT 43,608</div>
                            </div>
                        </div>
                        <div class="flex items-center gap-2 whitespace-nowrap">
                            <div class="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                                <span class="text-xs font-bold text-red-600">QR</span>
                            </div>
                            <div>
                                <div class="text-sm font-medium">QR</div>
                                <div class="text-xs text-gray-500">BDT 52,075</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Flight Cards -->
                <div class="space-y-4">
                    <?php echo $__env->make('components.flight-card', [
                        'airlineName' => 'USBangla Airlines',
                        'flightNumber' => 'BS-341',
                        'departureAirport' => 'DAC',
                        'departureAirportName' => 'Hazrat Shahjalal Intl Airport',
                        'departureTime' => '21:35',
                        'departureDate' => 'Sat 31 May 2025',
                        'arrivalAirport' => 'DXB',
                        'arrivalAirportName' => 'Dubai Intl Airport',
                        'arrivalTime' => '01:25',
                        'arrivalDate' => 'Sun 01 Jun 2025',
                        'duration' => '5H 50Min',
                        'stops' => 0,
                        'price' => 31492,
                        'originalPrice' => 33847,
                        'refundable' => true,
                        'cabinClass' => 'Economy'
                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <?php echo $__env->make('components.flight-card', [
                        'airlineName' => 'SriLankan Airlines',
                        'flightNumber' => 'UL-190, UL-225',
                        'departureAirport' => 'DAC',
                        'departureAirportName' => 'Hazrat Shahjalal Intl Airport',
                        'departureTime' => '22:55',
                        'departureDate' => 'Sat 31 May 2025',
                        'arrivalAirport' => 'DXB',
                        'arrivalAirportName' => 'Dubai Intl Airport',
                        'arrivalTime' => '21:40',
                        'arrivalDate' => 'Sat 31 May 2025',
                        'duration' => '10H 45Min',
                        'stops' => 1,
                        'price' => 32022,
                        'originalPrice' => 33715,
                        'refundable' => true,
                        'cabinClass' => 'Economy',
                        'layover' => 'Colombo (CMB) - 3h 20m'
                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                    <?php echo $__env->make('components.flight-card', [
                        'airlineName' => 'Fly Dubai',
                        'flightNumber' => 'FZ-584',
                        'departureAirport' => 'DAC',
                        'departureAirportName' => 'Hazrat Shahjalal Intl Airport',
                        'departureTime' => '22:00',
                        'departureDate' => 'Sat 31 May 2025',
                        'arrivalAirport' => 'DXB',
                        'arrivalAirportName' => 'Dubai Intl Airport',
                        'arrivalTime' => '01:20',
                        'arrivalDate' => 'Sun 01 Jun 2025',
                        'duration' => '5H 20Min',
                        'stops' => 0,
                        'price' => 42011,
                        'originalPrice' => 42011,
                        'refundable' => true,
                        'cabinClass' => 'Economy'
                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Flight details toggle functionality
    const detailButtons = document.querySelectorAll('[data-toggle="flight-details"]');

    detailButtons.forEach(button => {
        button.addEventListener('click', function() {
            const flightCard = this.closest('.flight-card');
            const detailsSection = flightCard.querySelector('.flight-details-section');
            const chevron = this.querySelector('svg:last-child');

            if (detailsSection.classList.contains('hidden')) {
                detailsSection.classList.remove('hidden');
                chevron.style.transform = 'rotate(180deg)';
            } else {
                detailsSection.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        });
    });

    // Book now functionality
    const bookButtons = document.querySelectorAll('[data-action="book-flight"]');

    bookButtons.forEach(button => {
        button.addEventListener('click', function() {
            const flightData = this.dataset.flightData;
            // Handle booking logic here
            console.log('Booking flight:', flightData);
            alert('Booking functionality would be implemented here');
        });
    });

    // Filter functionality
    const filterInputs = document.querySelectorAll('input[type="checkbox"], input[type="range"]');

    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Handle filter changes here
            console.log('Filter changed:', this.name, this.value || this.checked);
            // You would implement actual filtering logic here
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\xampp\htdocs\albaraka\resources\views/flights/search.blade.php ENDPATH**/ ?>