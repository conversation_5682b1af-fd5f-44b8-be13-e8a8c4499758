<div class="border rounded-lg shadow-sm bg-white mb-4 hover:shadow-md transition-shadow flight-card">
    <!-- Flight Header -->
    <div class="p-4 border-b border-gray-100">
        <div class="flex justify-between items-start">
            <div class="flex items-center gap-3">
                <div class="w-10 h-10 rounded-full border-2 border-gray-200 flex items-center justify-center bg-blue-50">
                    <?php if(isset($logoUrl) && $logoUrl): ?>
                        <img src="<?php echo e($logoUrl); ?>" alt="<?php echo e($airlineName ?? 'Airline'); ?>" class="w-6 h-6 object-contain" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <div class="text-xs font-bold text-blue-600 hidden"><?php echo e(substr($airlineName ?? 'AL', 0, 2)); ?></div>
                    <?php else: ?>
                        <div class="text-xs font-bold text-blue-600"><?php echo e(substr($airlineName ?? 'AL', 0, 2)); ?></div>
                    <?php endif; ?>
                </div>
                <div>
                    <p class="font-medium text-gray-900"><?php echo e($airlineName ?? 'Unknown Airline'); ?></p>
                    <p class="text-sm text-gray-500"><?php echo e($flightNumber ?? 'N/A'); ?></p>
                </div>
            </div>
            <div class="text-right">
                <p class="text-2xl font-bold text-orange-600">৳ <?php echo e(number_format($price ?? 0, 0)); ?></p>
                <?php if(isset($originalPrice) && $originalPrice > $price): ?>
                    <p class="text-sm text-gray-400 line-through">৳ <?php echo e(number_format($originalPrice, 0)); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Flight Route Information -->
    <div class="p-4">
        <div class="flex items-center justify-between">
            <!-- Departure -->
            <div class="text-center flex-1">
                <div class="text-2xl font-bold text-gray-900"><?php echo e($departureAirport ?? 'DAC'); ?></div>
                <div class="text-sm text-gray-600 mt-1"><?php echo e($departureAirportName ?? 'Hazrat Shahjalal Intl Airport'); ?></div>
                <div class="text-lg font-semibold text-gray-900 mt-2"><?php echo e($departureTime ?? '00:00'); ?></div>
                <div class="text-sm text-gray-500"><?php echo e($departureDate ?? 'Date'); ?></div>
            </div>

            <!-- Flight Path -->
            <div class="flex-1 px-4">
                <div class="flex items-center justify-center">
                    <div class="flex-1 border-t-2 border-gray-300"></div>
                    <div class="mx-3">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                        </svg>
                    </div>
                    <div class="flex-1 border-t-2 border-gray-300"></div>
                </div>
                <div class="text-center mt-2">
                    <div class="text-sm font-medium text-gray-700"><?php echo e($duration ?? '0h 00m'); ?></div>
                    <div class="text-xs text-gray-500">
                        <?php if(($stops ?? 0) > 0): ?>
                            <?php echo e($stops); ?> STOP<?php echo e($stops > 1 ? 'S' : ''); ?>

                        <?php else: ?>
                            NON STOP
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Arrival -->
            <div class="text-center flex-1">
                <div class="text-2xl font-bold text-gray-900"><?php echo e($arrivalAirport ?? 'DXB'); ?></div>
                <div class="text-sm text-gray-600 mt-1"><?php echo e($arrivalAirportName ?? 'Dubai Intl Airport'); ?></div>
                <div class="text-lg font-semibold text-gray-900 mt-2"><?php echo e($arrivalTime ?? '00:00'); ?></div>
                <div class="text-sm text-gray-500"><?php echo e($arrivalDate ?? 'Date'); ?></div>
            </div>
        </div>

        <!-- Flight Details -->
        <div class="mt-4 flex items-center justify-between text-sm">
            <div class="flex items-center gap-6">
                <!-- Refundable Status -->
                <div class="flex items-center gap-1">
                    <span class="w-2 h-2 rounded-full <?php echo e(($refundable ?? true) ? 'bg-green-500' : 'bg-red-500'); ?>"></span>
                    <span class="text-gray-600"><?php echo e(($refundable ?? true) ? 'Refundable' : 'Non Refundable'); ?></span>
                </div>

                <!-- Cabin Class -->
                <div class="text-gray-600">
                    <span class="font-medium"><?php echo e($cabinClass ?? 'Economy'); ?></span>
                </div>

                <!-- Baggage -->
                <?php if(isset($baggage)): ?>
                    <div class="flex items-center gap-1">
                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                        </svg>
                        <span class="text-gray-600"><?php echo e($baggage); ?></span>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Baggage Weight -->
            <div class="text-gray-600">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                30 Kg
            </div>
        </div>

        <!-- Layover Information -->
        <?php if(!empty($layover)): ?>
            <div class="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                <span class="text-yellow-800 font-medium">Layover: </span>
                <span class="text-yellow-700"><?php echo e($layover); ?></span>
            </div>
        <?php endif; ?>
    </div>

    <!-- Expandable Flight Details Section -->
    <div class="flight-details-section hidden border-t border-gray-100 bg-gray-50">
        <div class="p-4">
            <h4 class="font-semibold text-gray-900 mb-3">Flight Details</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <p class="font-medium text-gray-700">Aircraft Type</p>
                    <p class="text-gray-600"><?php echo e($aircraftType ?? 'Boeing 737-800'); ?></p>
                </div>
                <div>
                    <p class="font-medium text-gray-700">Meal Service</p>
                    <p class="text-gray-600"><?php echo e($mealService ?? 'Complimentary meal'); ?></p>
                </div>
                <div>
                    <p class="font-medium text-gray-700">Seat Selection</p>
                    <p class="text-gray-600"><?php echo e($seatSelection ?? 'Available for fee'); ?></p>
                </div>
                <div>
                    <p class="font-medium text-gray-700">Cancellation</p>
                    <p class="text-gray-600"><?php echo e($cancellationPolicy ?? 'Cancellation fee applies'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Footer -->
    <div class="px-4 py-3 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
        <button data-toggle="flight-details" class="text-blue-600 hover:text-blue-800 font-medium text-sm flex items-center gap-1 transition-colors">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            FLIGHT DETAILS
            <svg class="w-4 h-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
        </button>

        <button data-action="book-flight" data-flight-data="<?php echo e(json_encode([
            'airline' => $airlineName ?? 'Unknown',
            'flightNumber' => $flightNumber ?? 'N/A',
            'price' => $price ?? 0,
            'departure' => $departureAirport ?? 'DAC',
            'arrival' => $arrivalAirport ?? 'DXB'
        ])); ?>" class="bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-2 rounded transition-colors duration-200 flex items-center gap-2">
            BOOK NOW
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
        </button>
    </div>
</div><?php /**PATH E:\xampp\htdocs\albaraka\resources\views/components/flight-card.blade.php ENDPATH**/ ?>