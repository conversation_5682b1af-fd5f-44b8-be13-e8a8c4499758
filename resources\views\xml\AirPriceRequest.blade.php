<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
        <AirPriceReq TraceId="{{ uniqid('price-') }}" TargetBranch="{{ $targetBranch }}" CheckOBFees="TicketingOnly"
            FareRuleType="long" CheckFlightDetails="true" xmlns="http://www.travelport.com/schema/air_v52_0">
            <BillingPointOfSaleInfo OriginApplication="UAPI" xmlns="http://www.travelport.com/schema/common_v52_0" />
            <AirItinerary>

                @foreach ($solution['segments'] as $segment)


                <AirSegment Key="{{ $segment['key'] }}" AvailabilitySource="{{ $segment['availability']['availabilitySource'] }}" Equipment="{{ $segment['equipment'] }}"
                    OptionalServicesIndicator="false" ETicketability="Yes" LinkAvailability="true"
                    AvailabilityDisplayType="Fare Shop/Optimal Shop" Group="0" Carrier="{{ $segment['carrier'] }}" FlightNumber="{{ $segment['flightNumber'] }}"
                    Origin="{{ $segment['origin'] }}" Destination="{{ $segment['destination'] }}" DepartureTime="{{ $segment['DepartureTime'] }}"
                    ArrivalTime="{{ $segment['ArrivalTime'] }}" FlightTime="{{ $segment['flightTime'] }}" Distance="{{ $segment['distance'] }}" ProviderCode="1G"
                    ParticipantLevel="Secure Sell" PolledAvailabilityOption="Cached status used. Polled avail exists">
                    <AirAvailInfo ProviderCode="1G" />
                    <Connection />
                </AirSegment>

                @endforeach


                {{-- <AirSegment Key="aK0yWiTqWDKACvUrLAAAAA==" NumberOfStops="1" AvailabilitySource="Q" Equipment="77W"
                    OptionalServicesIndicator="false" ETicketability="Yes" LinkAvailability="true"
                    AvailabilityDisplayType="Fare Shop/Optimal Shop" Group="0" Carrier="EK" FlightNumber="209"
                    Origin="DXB" Destination="EWR" DepartureTime="2023-08-08T10:50:00.000+04:00"
                    ArrivalTime="2023-08-08T21:20:00.000-04:00" FlightTime="1110" Distance="6846" ProviderCode="1G"
                    ParticipantLevel="Secure Sell" PolledAvailabilityOption="Cached status used. Polled avail exists">
                    <AirAvailInfo ProviderCode="1G" />
                </AirSegment>
                <AirSegment Key="aK0yWiTqWDKAFvUrLAAAAA==" NumberOfStops="1" AvailabilitySource="Q" Equipment="77W"
                    OptionalServicesIndicator="false" ETicketability="Yes" LinkAvailability="true"
                    AvailabilityDisplayType="Fare Shop/Optimal Shop" Group="1" Carrier="EK" FlightNumber="210"
                    Origin="EWR" Destination="DXB" DepartureTime="2023-08-22T23:55:00.000-04:00"
                    ArrivalTime="2023-08-23T23:35:00.000+04:00" FlightTime="940" Distance="6846" ProviderCode="1G"
                    ParticipantLevel="Secure Sell" PolledAvailabilityOption="Cached status used. Polled avail exists">
                    <AirAvailInfo ProviderCode="1G" />
                    <Connection />
                </AirSegment>
                <AirSegment Key="aK0yWiTqWDKAJvUrLAAAAA==" AvailabilitySource="Q" Equipment="77W"
                    OptionalServicesIndicator="false" ETicketability="Yes" LinkAvailability="true"
                    AvailabilityDisplayType="Fare Shop/Optimal Shop" Group="1" Carrier="EK" FlightNumber="582"
                    Origin="DXB" Destination="DAC" DepartureTime="2023-08-24T02:00:00.000+04:00"
                    ArrivalTime="2023-08-24T08:40:00.000+06:00" FlightTime="280" Distance="2207" ProviderCode="1G"
                    ParticipantLevel="Secure Sell" PolledAvailabilityOption="Cached status used. Polled avail exists">
                    <AirAvailInfo ProviderCode="1G" />
                </AirSegment> --}}


            </AirItinerary>
            <AirPricingModifiers ETicketability="Required" PlatingCarrier="{{ $solution['platingCarrier'] }}" FaresIndicator="PublicAndPrivateFares" />
            <SearchPassenger Code="ADT" BookingTravelerRef="ADT_0"
                xmlns="http://www.travelport.com/schema/common_v52_0" />


            {{-- <SearchPassenger Code="CNN" Age="8" BookingTravelerRef="CNN_0"
                xmlns="http://www.travelport.com/schema/common_v52_0" />
            <SearchPassenger Code="INF" Age="1" BookingTravelerRef="INF_0"
                xmlns="http://www.travelport.com/schema/common_v52_0" /> --}}


            <AirPricingCommand />
        </AirPriceReq>
    </s:Body>
</s:Envelope>


{{-- 

<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
        <AirPriceReq xmlns:air="http://www.travelport.com/schema/air_v51_0"
            xmlns:com="http://www.travelport.com/schema/common_v51_0" TargetBranch="{{ $targetBranch }}"
            AuthorizedBy="{{ $username }}" TraceId="{{ uniqid('bd-price-') }}" SolutionResult="true">
            <!-- POS Info -->
            <BillingPointOfSaleInfo OriginApplication="UAPI" />


            <AirItinerary>

                <!-- Air Segment -->
                @foreach ($solution['segments'] as $segment)
                    <AirSegment Key="{{ $segment['key'] }}" Group="0" Carrier="{{ $segment['carrier'] }}"
                        FlightNumber="{{ $segment['flightNumber'] }}" Origin="{{ $segment['origin'] }}"
                        Destination="{{ $segment['destination'] }}" DepartureTime="{{ $segment['DepartureTime'] }}"
                        ArrivalTime="{{ $segment['ArrivalTime'] }}" FlightTime="{{ $segment['flightTime'] }}"
                        Distance="{{ $segment['distance'] }}" ETicketability="Yes"
                        Equipment="{{ $segment['equipment'] }}" ChangeOfPlane="false" ParticipantLevel="Secure Sell"
                        PolledAvailabilityOption="No polled avail exists" OptionalServicesIndicator="false"
                        AvailabilitySource="{{ $segment['availability']['availabilitySource'] }}"
                        AvailabilityDisplayType="Fare Shop/Optimal Shop">
                        <BookingInfo BookingCode="{{ $solution['bookingClass'] }}"
                            CabinClass="{{ $solution['cabinClass'] }}" FareInfoRef="{{ $segment['key'] }}" />
                        <AirAvailInfo ProviderCode="1G" />
                        <FlightDetailsRef Key="{{ $segment['flightDetailsRef'] }}" />
                    </AirSegment>
                @endforeach
                <!-- Air Segment -->
            </AirItinerary>





            <!-- Passengers -->
            <AirPricePassenger>
                <PassengerTypeCode>ADT</PassengerTypeCode> <!-- Adult -->
            </AirPricePassenger>

            <!-- Fare Info -->
            @foreach ($solution['fareInfo'] as $fareinfo)
                <FareInfo Key="{{ $fareinfo['key'] }}" FareBasis="{{ $fareinfo['fareBasis'] }}"
                    PassengerTypeCode="{{ $fareinfo['PassengerTypeCode'] }}" Origin="{{ $fareinfo['origin'] }}"
                    Destination="{{ $fareinfo['destination'] }}" EffectiveDate="{{ $fareinfo['EffectiveDate'] }}"
                    DepartureDate="{{ $fareinfo['DepartureDate'] }}" Amount="{{ $fareinfo['amount'] }}"
                    NegotiatedFare="false" NotValidBefore="{{ $fareinfo['NotValidBefore'] }}"
                    NotValidAfter="{{ $fareinfo['NotValidAfter'] }}">
                    <BaggageAllowance>
                        <MaxWeight Value="{{ $fareinfo['baggage']['value'] }}"
                            Unit="{{ $fareinfo['baggage']['unit'] }}" />
                    </BaggageAllowance>
                    <FareRuleKey FareInfoRef="{{ $fareinfo['FareInfoRef'] }}" ProviderCode="1G">
                        {{ $fareinfo['fareRuleKey'] }}</FareRuleKey>
                </FareInfo>
            @endforeach
            <!-- Price Quote -->
            <HostToken Key="HOST_TOKEN_1G" Host="1G" />

        </AirPriceReq>
    </s:Body>
</s:Envelope> --}}
