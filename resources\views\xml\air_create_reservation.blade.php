<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">

        <AirCreateReservationReq xmlns="http://www.travelport.com/schema/universal_v52_0"
            TargetBranch="{{ $targetBranch }}" AuthorizedBy="{{ $username }}" TraceId="{{ uniqid('bd-booking-') }}"
            SolutionResult="1">



            <BillingPointOfSaleInfo OriginApplication="UAPI" xmlns:com="http://www.travelport.com/schema/common_v52_0" />

            <BookingTraveler xmlns="http://www.travelport.com/schema/common_v52_0" Key="gr8AVWGCR064r57Jt0+8bA==0"
                TravelerType="ADT" DOB="2011-06-12" Gender="M" Nationality="BD">
                <BookingTravelerName Prefix="MR" First="ASHRAF" Last="AHMED" />
                <PhoneNumber Number="01712824016" />
                <Email Type="Home" EmailID="<EMAIL>" />
                <SSR SegmentRef="zSGyWiSqWDKAcWrrLAAAAA==" Type="DOCS"
                    FreeText="P/BD/BK0277878/BD/12Jun11/M/01Jan28/AHMED/ASHRAF" Carrier="EK" />
                <SSR Type="CTCM" Status="HK" FreeText="01712824016" Carrier="EK" />
                <SSR Type="CTCE" Status="HK" FreeText="ashraf//a4aero.com" Carrier="EK" />
            </BookingTraveler>

            <ContinuityCheckOverride xmlns="http://www.travelport.com/schema/common_v52_0">Yes</ContinuityCheckOverride>
            <FormOfPayment xmlns="http://www.travelport.com/schema/common_v52_0" Type="Cash" IsAgentType="true" />


            <AirPricingSolution xmlns="http://www.travelport.com/schema/air_v52_0" Key="zSGyWiSqWDKAmWrrLAAAAA=="
                QuoteDate="2023-06-12" TotalPrice="BDT273658" BasePrice="USD2099.00" ApproximateTotalPrice="BDT273658"
                ApproximateBasePrice="BDT226315" EquivalentBasePrice="BDT226315" Taxes="BDT47343" Fees="BDT0"
                ApproximateTaxes="BDT47343">

                <AirSegment Key="zSGyWiSqWDKAcWrrLAAAAA==" AvailabilitySource="S" Equipment="77W"
                    OptionalServicesIndicator="false" AvailabilityDisplayType="Fare Specific Fare Quote Unbooked"
                    Group="0" Carrier="EK" FlightNumber="585" Origin="DAC" Destination="DXB"
                    DepartureTime="2023-08-08T01:40:00.000+06:00" ArrivalTime="2023-08-08T04:30:00.000+04:00"
                    FlightTime="290" TravelTime="290" Distance="2207" ProviderCode="1G" ParticipantLevel="Secure Sell"
                    PolledAvailabilityOption="O and D cache or polled status used with different local status"
                    ClassOfService="L">
                    <CodeshareInfo OperatingCarrier="EK">Emirates</CodeshareInfo>
                    <AirAvailInfo ProviderCode="1G" />
                    <FlightDetails TravelTime="290" FlightTime="290" ArrivalTime="2023-08-08T04:30:00.000+04:00"
                        DepartureTime="2023-08-08T01:40:00.000+06:00" Destination="DXB" Origin="DAC"
                        Key="zSGyWiSqWDKAdWrrLAAAAA==" />
                    <Connection />
                </AirSegment>

                <AirPricingInfo LatestTicketingTime="2023-06-30T23:59:00.000+06:00" PricingMethod="Guaranteed"
                    BasePrice="USD1133.00" Key="zSGyWiSqWDKAtWrrLAAAAA==" TotalPrice="BDT142078"
                    ApproximateTotalPrice="BDT142078" ApproximateBasePrice="BDT122160" EquivalentBasePrice="BDT122160"
                    Taxes="BDT19918" ApproximateTaxes="BDT19918" ProviderCode="1G">

                    <FareInfo EffectiveDate="2023-06-12T14:47:00.000+06:00" Amount="BDT64272" Destination="EWR"
                        Origin="DAC" PassengerTypeCode="ADT" FareBasis="LXAAPBD1" Key="zSGyWiSqWDKA8WrrLAAAAA=="
                        TaxAmount="BDT15189.00">
                        <Endorsement xmlns="http://www.travelport.com/schema/common_v52_0" Value="NON-END/SAVER" />
                        <Endorsement xmlns="http://www.travelport.com/schema/common_v52_0"
                            Value="REWARD UPGDS ALLOWED" />
                        <FareRuleKey FareInfoRef="zSGyWiSqWDKA8WrrLAAAAA==" ProviderCode="1G">
                            6UUVoSldxwgldbun3TGS1cbKj3F8T9EyxsqPcXxP0TLGyo9xfE/RMsuWFfXVd1OAly5qxZ3qLwOXLmrFneovA5cuasWd6i8Dly5qxZ3qLwOXLmrFneovAxsq9Xrmw+DmxWa1uaqI55k3aSkvhp2ybXeaohNgo3d8oOoigVeBNl/AyGqh8JIaqOnT1VPBcw/OGOSH77AcGgZeirK1ExYO8+r/bRGkSYBeVvL0gTWvGVKnsahilB5/WJLKVanbw7tLZ2vqEvHvGtBS8ndYUmM13YB1xjSbRrjKTicpvYbmY4Wr6XufjmxNIaHcXN1Nsaqcir5wkVQHOuKXLmrFneovA5cuasWd6i8Dly5qxZ3qLwOXLmrFneovA5cuasWd6i8Dc3mNX1GvOry3EdbqPPDZRr2QFwD3/qYpuedM5Dut5bDwuQrdW/CAEi/+I81ZXfEyYq7nMtlnk50=
                        </FareRuleKey>
                        <Brand Key="zSGyWiSqWDKA8WrrLAAAAA==" />
                    </FareInfo>

                    <BookingInfo BookingCode="L" CabinClass="Economy" FareInfoRef="zSGyWiSqWDKA8WrrLAAAAA=="
                        SegmentRef="zSGyWiSqWDKAcWrrLAAAAA==" HostTokenRef="zSGyWiSqWDKAnWrrLAAAAA==" />

                    <!-- Baggage Allowance -->
                    @if (isset($solution['fareInfo'][0]['baggage']))
                        <air:BaggageAllowance Weight="{{ $solution['fareInfo'][0]['baggage']['value'] }}"
                            Unit="{{ $solution['fareInfo'][0]['baggage']['unit'] }}" />
                    @endif
                    </air:AirPricingInfo>
                    </air:AirPricingSolution>


                    <!-- Action Status -->
                    <ActionStatus xmlns="http://www.travelport.com/schema/common_v52_0" Type="TAU"
                        TicketDate="{{ date('Y-m-d\TH:i:s', strtotime($solution['ticketingTime'])) }}"
                        ProviderCode="1G" />



        </AirCreateReservationReq>
    </s:Body>
</s:Envelope>
