<?php

use App\Http\Controllers\Frontend\FlightController;
use App\Http\Controllers\Frontend\IndexController;
use App\Http\Controllers\Frontend\SabreController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;


Route::any('/',[IndexController::class, 'index'])->name('index');
Route::any('/new',[IndexController::class, 'new'])->name('new');
Route::any('/flight',[FlightController::class, 'search'])->name('flights.search');
Route::any('/flight-list',[FlightController::class, 'search'])->name('flights.list');
Route::any('/flight-select',[FlightController::class, 'select_flight'])->name('flights.booking');
Route::post('/flight-AirPrice',[FlightController::class, 'airPrice'])->name('flight.get_air_price');
Route::post('/flight-initiate',[FlightController::class, 'initiateBooking'])->name('flight.booking.initiate');

Route::any('/flight-list',[SabreController::class, 'flightList'])->name('flights.list');

// Route::get('/', function () {
//     return view('welcome');
// });

// Auth::routes();


Route::any('/sabretoken',[SabreController::class, 'getToken'])->name('getToken');
Route::any('/sabre',[IndexController::class, 'sabre'])->name('sabre');
Route::any('/sabre-flight',[SabreController::class, 'searchFlights'])->name('sabre.search');

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
