<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="style.css" />
    <link
      href="https://cdn.jsdelivr.net/npm/daisyui@4.6.0/dist/full.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script
      src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"
      defer
    ></script>
    <script
      src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"
      defer
    ></script>
    <!-- ✅ Font Awesome CDN -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
      integrity="sha512-..."
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <title>Flight Search UI</title>
  </head>
  <body>
    <section class="bg-zinc-100 w-full pb-28">
      <div class="flex justify-between pt-5 px-20">
        <p>
          <i class="fa-solid fa-envelope text-orange-600"> </i> Email :
          care@travomint,com
        </p>
        <div class="flex gap-2">
          <p>
            <i class="fa-solid fa-phone-volume text-orange-600"></i> Number : +
            +880-1972076510
          </p>
          <span class="text-gray-400">|</span>
          <p>
            <i class="fa-solid fa-calendar-days text-orange-600"></i> Find
            Booking
          </p>
        </div>
      </div>
      <p class="border-t border-gray-200 my-4"></p>

      <!-- Navbar -->
      <nav class="px-20 py-3 flex items-center justify-between">
        <div class="text-4xl font-semibold text-orange-600">Travomint</div>

        <div class="flex items-center space-x-10">
          <ul
            class="hidden md:flex items-center space-x-10 text-gray-700 font-medium"
          >
            <li class="group cursor-pointer">
              <div class="nav-link text-black pb-1 flex items-center space-x-1">
                <span><i class="fa-solid fa-plane-departure"></i></span
                ><span>Flights</span>
              </div>
            </li>
            <hr class="h-12 min-w-[1px] bg-gray-300" />

            <li class="group cursor-pointer">
              <div class="nav-link flex items-center space-x-1">
                <span><i class="fa-solid fa-bed"></i></span><span>Hotels</span>
              </div>
            </li>
            <hr class="h-12 min-w-[1px] bg-gray-300" />

            <li class="group cursor-pointer">
              <div class="nav-link flex items-center space-x-1">
                <span><i class="fa-solid fa-umbrella-beach"></i></span
                ><span>Vacation</span>
              </div>
            </li>
            <hr class="h-12 min-w-[1px] bg-gray-300" />

            <li class="group cursor-pointer">
              <div class="nav-link flex items-center space-x-1">
                <span><i class="fa-solid fa-car"></i></span
                ><span>Car Rental</span>
              </div>
            </li>
            <hr class="h-12 min-w-[1px] bg-gray-300" />

            <li class="group cursor-pointer">
              <div class="nav-link flex items-center space-x-1">
                <span><i class="fa-solid fa-sliders"></i></span
                ><span>Transfers</span>
              </div>
            </li>
          </ul>
        </div>

        <div class="flex items-center space-x-4 md:space-x-6 text-sm">
          <button
            class="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center"
          >
            <span class="ml-1 text-lg py-1">
              <i class="fa-solid fa-circle-user px-2"></i>Log in/Sign up
            </span>
          </button>
          <hr class="h-8 min-w-[1px] bg-gray-300" />
          <div class="flex items-center space-x-2">
            <img
              src="https://flagcdn.com/w40/bd.png"
              alt="BD"
              class="w-6 h-4 object-cover rounded-sm"
            />
            <button class="text-gray-700 text-lg">৳</button>
          </div>
        </div>
      </nav>
      <!-- Trip Type -->
      <div class="flex space-x-6 text-lg font-medium mb-6 px-20 mt-10">
        <label
          class="flex items-center space-x-2 cursor-pointer text-orange-500"
        >
          <input
            type="radio"
            name="tripType"
            value="oneWay"
            checked
            class="accent-orange-500"
          />
          <span>One Way</span>
        </label>
        <label class="flex items-center space-x-2 cursor-pointer text-gray-700">
          <input
            type="radio"
            name="tripType"
            value="roundTrip"
            class="accent-orange-500"
          />
          <span>Round Trip</span>
        </label>
      </div>
      <section class="mx-10 mt-8 bg-white shadow-lg rounded-2xl px-6 py-8">
        <!-- Form Fields Row -->
        <div class="flex items-end justify-between gap-4">
          <!-- From & To with Connector -->
          <div class="flex items-center gap-4 relative">
            <!-- From -->

            <div
              class="relative w-[290px]"
              x-data="{ 
              open: false, 
              selected: null,
              cities: [
                { name: 'Barisal', code: 'BZR' },
                { name: 'Cox\'s Bazar', code: 'CXB' },
                { name: 'Jessore', code: 'JSR' },
                { name: 'Sylhet', code: 'ZYL' },
                { name: 'Saidpur', code: 'SPD' }
              ]
            }"
            >
              <label
                class="text-sm text-gray-500 absolute -top-2.5 left-3 bg-white px-1 z-10"
                >From</label
              >

              <!-- Display Box -->
              <div
                class="flex flex-col justify-center px-4 py-3 border rounded-xl bg-white shadow-sm space-y-1 h-[90px] cursor-pointer"
                @click="open = !open"
              >
                <div class="flex items-center space-x-2">
                  <span
                    class="font-bold text-xl text-black"
                    x-text="selected ? selected.name  : 'Leaving from'"
                  ></span>
                </div>
             
                <div
                  class="text-base text-gray-400"
                  x-show="selected"
                  x-text="selected.code + ', ' + selected.name"
                ></div>
              </div>

              <!-- Dropdown -->
              <div
                x-show="open"
                @click.outside="open = false"
                class="absolute top-[100%] mt-2 w-[370px] bg-white shadow-lg rounded-xl border z-50 max-h-[300px] overflow-y-auto"
              >
                <div class="px-4 py-8 text-base text-gray-500 border-b">
                  From
                </div>
                <div class="px-4 py-2 text-sm text-gray-500 border-b">
                  Suggested Cities
                </div>

                <template x-for="(city, index) in cities" :key="index">
                  <div
                    class="flex items-center justify-between px-4 py-3 hover:bg-gray-100 cursor-pointer border-b"
                    @click="selected = city; open = false"
                  >
                    <div class="flex gap-4">
                      <img
                        src="https://www.travomint.com/Image/aeroplane-drico.png"
                        class="w-6 h-4 mt-4"
                        alt="Plane Icon"
                      />
                      <div>
                        <div
                          class="font-semibold text-black"
                          x-text="city.name + ', Bangladesh'"
                        ></div>
                        <div
                          class="text-sm text-gray-500"
                          x-text="city.code + ', ' + city.name"
                        ></div>
                      </div>
                    </div>
                    <img
                      src="https://flagcdn.com/w40/bd.png"
                      alt="BD"
                      class="w-6 h-4 object-cover rounded-sm"
                    />
                  </div>
                </template>
              </div>
            </div>

            <!-- Connector Icon (overlapping style) -->
            <div class="absolute left-[50%] translate-x-[-50%] z-20">
              <div
                class="w-10 h-10 rounded-full bg-orange-500 flex items-center justify-center shadow-md"
              >
                <i class="fas fa-right-left text-white"></i>
              </div>
            </div>

            <!-- To -->
         
            <div
              class="relative w-[290px]"
              x-data="{ 
              open: false, 
              selected: null,
              cities: [
                { name: 'Barisal', code: 'BZR' },
                { name: 'Cox\'s Bazar', code: 'CXB' },
                { name: 'Jessore', code: 'JSR' },
                { name: 'Sylhet', code: 'ZYL' },
                { name: 'Saidpur', code: 'SPD' }
              ]
            }"
            >
              <label
                class="text-sm text-gray-500 absolute -top-2.5 left-3 bg-white px-1 z-10"
                >To</label
              >

              <!-- Display Box -->
              <div
                class="flex flex-col justify-center px-4 py-3 border rounded-xl bg-white shadow-sm space-y-1 h-[90px] cursor-pointer"
                @click="open = !open"
              >
                <div class="flex items-center space-x-2">
                  <span
                    class="font-bold text-xl text-black"
                    x-text="selected ? selected.name  : 'Going To'"
                  ></span>
                </div>
                <!-- Show code + name below -->
                <div
                  class="text-base text-gray-400"
                  x-show="selected"
                  x-text="selected.code + ', ' + selected.name"
                ></div>
              </div>

              <!-- Dropdown -->
              <div
                x-show="open"
                @click.outside="open = false"
                class="absolute top-[100%] mt-2 w-[370px] bg-white shadow-lg rounded-xl border z-50 max-h-[300px] overflow-y-auto"
              >
                <div class="px-4 py-8 text-base text-gray-500 border-b">To</div>
                <div class="px-4 py-2 text-sm text-gray-500 border-b">
                  Suggested Cities
                </div>

                <template x-for="(city, index) in cities" :key="index">
                  <div
                    class="flex items-center justify-between px-4 py-3 hover:bg-gray-100 cursor-pointer border-b"
                    @click="selected = city; open = false"
                  >
                    <div class="flex gap-4">
                      <img
                        src="https://www.travomint.com/Image/aeroplane-drico.png"
                        class="w-6 h-4 mt-4"
                        alt="Plane Icon"
                      />
                      <div>
                        <div
                          class="font-semibold text-black"
                          x-text="city.name + ', Bangladesh'"
                        ></div>
                        <div
                          class="text-sm text-gray-500"
                          x-text="city.code + ', ' + city.name"
                        ></div>
                      </div>
                    </div>
                    <img
                      src="https://flagcdn.com/w40/bd.png"
                      alt="BD"
                      class="w-6 h-4 object-cover rounded-sm"
                    />
                  </div>
                </template>
              </div>
            </div>
          </div>

          <!-- Departure -->
          
          <div
          x-data="{
            open: false,
            selectedDate: new Date(),
            currentMonth: new Date(),
            get daysInMonth() {
              const y = this.currentMonth.getFullYear(),
                    m = this.currentMonth.getMonth(),
                    total = new Date(y, m + 1, 0).getDate();
              return Array.from({ length: total }, (_, i) => new Date(y, m, i + 1));
            },
            get blanks() {
              const firstDay = new Date(
                this.currentMonth.getFullYear(),
                this.currentMonth.getMonth(),
                1
              ).getDay();
              return Array.from({ length: firstDay });
            },
            selectDate(day) {
              this.selectedDate = day;
              this.open = false;
            }
          }"
          class="relative  min-w-[180px]"
        >
          <!-- Trigger Box -->
          <div
            @click="open = !open"
            class="relative px-4 py-4 border rounded-xl bg-white shadow-sm h-[90px] cursor-pointer"
          >
            <label
              class="absolute -top-2.5 left-3 bg-white px-1 text-sm text-gray-500 z-10"
            >
              Departure
            </label>
        <div class="flex gap-5">
        <div>
          <div class="font-bold text-2xl">
            <span x-text="selectedDate.getDate()"></span>
            <span
              class="text-lg ml-1 font-normal"
              x-text="`${selectedDate.toLocaleString('en-us',{month:'short'})}'${selectedDate.getFullYear()}`"
            ></span>
          </div>
         
          <div
            class="text-sm text-gray-500"
            x-text="selectedDate.toLocaleDateString('en-us',{weekday:'long'})"
          ></div>
        </div>
          <div class="mt-2 text-orange-600"><i class="fa-solid fa-angle-down"></i></div>
        </div>
          </div>
        
          <!-- Calendar Popup -->
          <div
            x-show="open"
            @click.outside="open = false"
            x-transition
            class="absolute top-[110%] w-[370px] bg-white rounded-xl border border-gray-300 shadow-xl z-50"
          >
            <!-- Header -->
            <div class="flex items-center justify-between px-4 py-3">
              <div
                class="text-lg font-semibold"
                x-text="currentMonth.toLocaleString('en-us',{month:'long', year:'numeric'})"
              ></div>
              <div class="flex gap-2">
                <button class="border px-4 py-2 rounded-lg text-gray-400">
                  <i class="fa-solid fa-angle-left"></i>
                </button>
                <button class="border px-4 py-2 rounded-lg text-gray-400">
                  <i class="fa-solid fa-angle-right"></i>
                </button>
              </div>
            </div>
        
            <!-- Week Days -->
            <div class="grid grid-cols-7 text-center text-sm font-medium text-gray-500 px-4 pt-3 pb-2">
              <template x-for="d in ['Sun','Mon','Tue','Wed','Thu','Fri','Sat']" :key="d">
                <div x-text="d"></div>
              </template>
            </div>
        
            <!-- Dates Grid -->
            <div class="grid grid-cols-7 divide-x divide-y divide-gray-200 text-center text-base py-2">
              <!-- Blank cells -->
              <template x-for="(_, i) in blanks" :key="'b'+i">
                <div class="h-12"></div>
              </template>
        
              <!-- Actual days -->
              <template x-for="day in daysInMonth" :key="day">
                <div
                  @click="selectDate(day)"
                  class="flex items-center justify-center h-12 cursor-pointer py-4 transition text-gray-500 font-medium"
                  :class="{
                    'bg-orange-500 rounded-md text-white': day.toDateString() === selectedDate.toDateString(),
                    'hover:bg-gray-100': day.toDateString() !== selectedDate.toDateString()
                  }"
                  x-text="day.getDate()"
                ></div>
              </template>
            </div>
          </div>
        </div>
        
          <!-- Return -->

          <div
          x-data="{
            open: false,
            selectedDate: null,
            currentMonth: new Date(),
            get daysInMonth() {
              const y = this.currentMonth.getFullYear(),
                    m = this.currentMonth.getMonth(),
                    total = new Date(y, m + 1, 0).getDate();
              return Array.from({ length: total }, (_, i) => new Date(y, m, i + 1));
            },
            get blanks() {
              const firstDay = new Date(
                this.currentMonth.getFullYear(),
                this.currentMonth.getMonth(),
                1
              ).getDay();
              return Array.from({ length: firstDay });
            },
            selectDate(day) {
              this.selectedDate = day;
              this.open = false;
            }
          }"
          class="relative min-w-[180px]"
        >
          <!-- Trigger Box -->
          <div
            @click="open = !open"
            class="relative px-4 py-4 border rounded-xl bg-white shadow-sm h-[90px] cursor-pointer"
          >
            <label
              class="absolute -top-2.5 left-3 bg-white px-1 text-sm text-gray-500 z-10"
            >
              Return
            </label>
        
            <!-- Date Display -->
            <div class="min-h-[40px]">
              <template x-if="!selectedDate">
                <div class="font-normal text-sm text-gray-500">
                  Book a round trip to <br> save more
                </div>
               
              </template>
        
              <template x-if="selectedDate">
              <div class="flex gap-4">  <div class="font-bold text-2xl">
                <span x-text="selectedDate.getDate()"></span>
                <span
                  class="text-lg ml-1 font-normal"
                  x-text="`${selectedDate.toLocaleString('en-us',{month:'short'})} '${selectedDate.getFullYear()}`"
                ></span>
               
              </div>
              <div class="mt-2 text-orange-600"><i class="fa-solid fa-angle-down"></i></div></div>
              </template>
            </div>
        
            <!-- Weekday -->
            <div
              class="text-sm text-gray-500"
              x-show="selectedDate"
              x-text="selectedDate.toLocaleDateString('en-us',{weekday:'long'})"
            ></div>
          </div>
        
          <!-- Calendar Popup -->
          <div
            x-show="open"
            @click.outside="open = false"
            x-transition
            class="absolute top-[110%] w-[370px] bg-white rounded-xl border border-gray-300 shadow-xl z-50"
          >
            <!-- Header -->
            <div class="flex items-center justify-between px-4 py-3">
              <div
                class="text-lg font-semibold"
                x-text="currentMonth.toLocaleString('en-us',{month:'long', year:'numeric'})"
              ></div>
              <div class="flex gap-2">
                <button
                  class="border px-4 py-2 rounded-lg text-gray-400"
                  @click="currentMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)"
                >
                  <i class="fa-solid fa-angle-left"></i>
                </button>
                <button
                  class="border px-4 py-2 rounded-lg text-gray-400"
                  @click="currentMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)"
                >
                  <i class="fa-solid fa-angle-right"></i>
                </button>
              </div>
            </div>
        
            <!-- Week Days -->
            <div class="grid grid-cols-7 text-center text-sm font-medium text-gray-500 px-4 pt-3 pb-2">
              <template x-for="d in ['Sun','Mon','Tue','Wed','Thu','Fri','Sat']" :key="d">
                <div x-text="d"></div>
              </template>
            </div>
        
            <!-- Dates Grid -->
            <div class="grid grid-cols-7 divide-x divide-y divide-gray-200 text-center text-base py-2">
              <!-- Blank cells -->
              <template x-for="(_, i) in blanks" :key="'b'+i">
                <div class="h-12"></div>
              </template>
        
              <!-- Actual days -->
              <template x-for="day in daysInMonth" :key="day">
                <div
                  @click="selectDate(day)"
                  class="flex items-center justify-center h-12 cursor-pointer py-4 transition text-gray-500 font-medium"
                  :class="{
                    'bg-orange-500 rounded-md text-white': selectedDate && day.toDateString() === selectedDate.toDateString(),
                    'hover:bg-gray-100': !selectedDate || day.toDateString() !== selectedDate?.toDateString()
                  }"
                  x-text="day.getDate()"
                ></div>
              </template>
            </div>
          </div>
        </div>
        


          <!-- Travelers -->
          <div
          class="relative w-[290px]"
          x-data="{
            open: false,
            travelers: {
              adult: 1,
              child: 0,
              infant: 0,
            },
            get total() {
              return this.travelers.adult + this.travelers.child + this.travelers.infant;
            }
          }"
        >
          <label
            class="text-sm text-gray-500 absolute -top-2.5 left-3 bg-white px-1 z-10"
          >
            Traveler(s) & Class
          </label>
        
          <!-- Display Box -->
          <div
            class="flex flex-col justify-center px-4 py-3 border rounded-xl bg-white shadow-sm space-y-1 h-[90px] cursor-pointer"
            @click="open = !open"
          >
          <div class="flex gap-5">
            <div class="font-bold text-2xl">
              <span>1</span>
              <span
                class="text-lg ml-1 font-normal"
               
              >Traveler</span>
              
            </div>
            <div class="mt-2 text-orange-600"><i class="fa-solid fa-angle-down"></i></div>
          </div>
            <div class="text-base text-gray-400">Economy</div>
          </div>
        
          <!-- Dropdown -->
          <div
            x-show="open"
            @click.outside="open = false"
            class="absolute top-[100%] mt-2 w-[370px] bg-white shadow-lg rounded-xl border z-50"
          >
 
            <!-- Traveler Row -->
            <template x-for="type in ['adult', 'child', 'infant']" :key="type">
              <div class="flex items-center justify-between px-4 py-3 hover:bg-gray-50 border-b">
                <div>
                  <div class="font-bold text-lg capitalize" x-text="type"></div>
                  <div class="text-sm font-semibold text-gray-500" x-text="type === 'adult' ? '(12+ years)' : type === 'child' ? '(2–11 years)' : '(Under 2 years)'"></div>
                </div>
                <div class="flex items-center gap-3">
                  <button
                    class="px-3 py-1 border rounded-lg text-lg bg-gray-100"
                  >−</button>
                  <span x-text="travelers[type]"></span>
                  <button
                    class="px-3 py-1 border rounded-lg text-lg bg-gray-100"
                  >+</button>
                </div>
              </div>
            </template>
            <p class="text-sm p-4">Travel Class</p>
            <div class="px-4 flex gap-4 my-2">
              <button class="bg-orange-600 w-full h-14 rounded-lg text-sm text-white">Economy</button>
              <button class="bg-slate-100 w-full h-14 rounded-lg text-sm "> Premium <br> Economy</button>
            </div>
            <div class="px-4 flex gap-4 my-2">
              <button class="bg-slate-100 w-full h-14 rounded-lg text-sm ">Business</button>
              <button class="bg-slate-100 w-full h-14 rounded-lg text-sm ">First</button>
            </div>
            <p class="uppercase text-orange-600 text-sm font-semibold border-t text-center p-4 mt-7">Done</p>
          </div>
        </div>
        
          
          <!-- Search Button -->
          <div class="min-w-[180px]">
            <button
              class="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-4 px-6 rounded-xl shadow h-[90px]"
            >
              SEARCH
            </button>
          </div>
        </div>
      </section>
    </section>
    <!-- top Search section -->
    <section class="px-20 my-10">
      <h2 class="text-2xl font-semibold text-slate-950 mb-5">Top Searches</h2>
      <div class="grid grid-cols-3 gap-8 h-full">
        <!-- Card 1 -->
        <div
          class="w-full flex justify-evenly bg-pink-100 py-2 px-2 rounded-lg h-full hover:scale-105 hover:shadow-xl transition-all duration-300"
        >
          <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
            <h2 class="text-base font-medium">DAC</h2>
            <p class="text-xs text-gray-600">04/12/2025</p>
          </div>
          <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
            <div class="flex gap-10">
              <img
                class="w-20 rounded-lg"
                src="https://www.travomint.com/_next/image?url=%2FImage%2Fplane-dashed-route.png&w=256&q=50"
                alt=""
              />
              <h2 class="text-base font-medium">LHR</h2>
            </div>
            <p class="text-xs text-gray-600">A-1,C-1,B-1 Lorem adipisicing.</p>
          </div>
          <div class="ml-10">
            <img
              class="w-20 h-20 rounded-lg"
              src="https://static.vecteezy.com/system/resources/thumbnails/036/054/418/small/ai-generated-landing-a-plane-against-a-golden-sky-at-sunset-passenger-aircraft-flying-up-in-sunset-light-travelling-and-business-concept-photo.jpg"
              alt=""
            />
          </div>
        </div>

        <!-- Card 2 -->
        <div
          class="w-full flex justify-evenly bg-pink-100 py-2 px-2 rounded-lg h-full hover:scale-105 hover:shadow-xl transition-all duration-300"
        >
          <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
            <h2 class="text-base font-medium">DAC</h2>
            <p class="text-xs text-gray-600">04/12/2025</p>
          </div>
          <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
            <div class="flex gap-10">
              <img
                class="w-20 rounded-lg"
                src="https://www.travomint.com/_next/image?url=%2FImage%2Fplane-dashed-route.png&w=256&q=50"
                alt=""
              />
              <h2 class="text-base font-medium">LHR</h2>
            </div>
            <p class="text-xs text-gray-600">A-1,C-1,B-1 Lorem adipisicing.</p>
          </div>
          <div class="ml-10">
            <img
              class="w-20 h-20 rounded-lg"
              src="https://static.vecteezy.com/system/resources/thumbnails/036/054/418/small/ai-generated-landing-a-plane-against-a-golden-sky-at-sunset-passenger-aircraft-flying-up-in-sunset-light-travelling-and-business-concept-photo.jpg"
              alt=""
            />
          </div>
        </div>

        <!-- Card 3 -->
        <div
          class="w-full flex justify-evenly bg-pink-100 py-2 px-2 rounded-lg h-full hover:scale-105 hover:shadow-xl transition-all duration-300"
        >
          <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
            <h2 class="text-base font-medium">DAC</h2>
            <p class="text-xs text-gray-600">04/12/2025</p>
          </div>
          <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
            <div class="flex gap-10">
              <img
                class="w-20 rounded-lg"
                src="https://www.travomint.com/_next/image?url=%2FImage%2Fplane-dashed-route.png&w=256&q=50"
                alt=""
              />
              <h2 class="text-base font-medium">LHR</h2>
            </div>
            <p class="text-xs text-gray-600">A-1,C-1,B-1 Lorem adipisicing.</p>
          </div>
          <div class="ml-10">
            <img
              class="w-20 h-20 rounded-lg"
              src="https://static.vecteezy.com/system/resources/thumbnails/036/054/418/small/ai-generated-landing-a-plane-against-a-golden-sky-at-sunset-passenger-aircraft-flying-up-in-sunset-light-travelling-and-business-concept-photo.jpg"
              alt=""
            />
          </div>
        </div>
      </div>
    </section>
    <!-- top airlines section -->
    <section class="relative pb-10">
      <div class="h-80 bg-pink-100 relative overflow-hidden">
        <svg
          class="absolute top-0 left-0 w-full"
          viewBox="0 0 1440 100"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0 C360,100 1080,100 1440,0 L1440,0 L0,0 Z"
            fill="#ffffff"
          />
        </svg>
      </div>
      <div class="relative z-10 -mt-48 px-20">
        <h2 class="text-2xl font-semibold text-slate-950 mb-5">
          Top Airline Deals
        </h2>
        <div class="grid grid-cols-6 gap-6">
          <!-- Repeatable Card -->
          <div
            class="w-full rounded-md shadow-md dark:bg-gray-50 dark:text-gray-800 p-4 bg-white relative group transition-all duration-300 hover:-translate-y-1 hover:scale-110 overflow-hidden"
          >
            <!-- Circle Burst on Hover Only -->
            <div
              class="absolute -top-10 -right-10 w-32 h-32 opacity-0 scale-75 translate-x-4 -translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-0"
            >
              <!-- Outer Circle -->
              <div
                class="absolute top-1 right-1 w-24 h-24 bg-orange-100 rounded-full z-10"
              ></div>
              <!-- Middle Circle -->
              <div
                class="absolute top-4 right-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
              ></div>
              <!-- Inner Circle -->
              <div
                class="absolute top-6 right-6 w-12 h-12 bg-orange-600 rounded-full z-30"
              ></div>
            </div>

            <!-- Airline Logo -->
            <img
              src="https://www.travomint.com/resources/images/airline-logo/BS.png?w=1920"
              alt=""
              class="w-16 relative z-40"
            />

            <!-- Route Info -->
            <div class="flex items-stretch gap-2 mt-2 relative z-40">
              <!-- Dots and line -->
              <div class="flex flex-col justify-between items-center">
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
                <div class="flex-1 w-[1px] bg-gray-600"></div>
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
              </div>

              <!-- Airport codes and date -->
              <div class="flex flex-col space-y-4">
                <h2 class="text-base font-medium leading-tight">DAC</h2>
                <p class="text-xs text-gray-600 leading-tight mt-[2px]">
                  04/24/2025
                </p>
                <h2 class="text-base font-medium leading-tight">CXB</h2>
              </div>
            </div>

            <!-- Price Normal -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 opacity-100 translate-x-0 transition-all duration-200 group-hover:opacity-0 group-hover:-translate-x-4 relative z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Price On Hover -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 absolute right-10 left-0 bottom-4 opacity-0 -translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-800 z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Bottom Border Animation -->
            <span
              class="absolute right-0 bottom-0 h-[1.5px] bg-orange-500 w-0 origin-right group-hover:w-full transition-all duration-300"
            ></span>
          </div>
          <div
            class="w-full rounded-md shadow-md dark:bg-gray-50 dark:text-gray-800 p-4 bg-white relative group transition-all duration-300 hover:-translate-y-1 hover:scale-110 overflow-hidden"
          >
            <!-- Circle Burst on Hover Only -->
            <div
              class="absolute -top-10 -right-10 w-32 h-32 opacity-0 scale-75 translate-x-4 -translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-0"
            >
              <!-- Outer Circle -->
              <div
                class="absolute top-1 right-1 w-24 h-24 bg-orange-100 rounded-full z-10"
              ></div>
              <!-- Middle Circle -->
              <div
                class="absolute top-4 right-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
              ></div>
              <!-- Inner Circle -->
              <div
                class="absolute top-6 right-6 w-12 h-12 bg-orange-600 rounded-full z-30"
              ></div>
            </div>

            <!-- Airline Logo -->
            <img
              src="https://www.travomint.com/resources/images/airline-logo/BS.png?w=1920"
              alt=""
              class="w-16 relative z-40"
            />

            <!-- Route Info -->
            <div class="flex items-stretch gap-2 mt-2 relative z-40">
              <!-- Dots and line -->
              <div class="flex flex-col justify-between items-center">
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
                <div class="flex-1 w-[1px] bg-gray-600"></div>
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
              </div>

              <!-- Airport codes and date -->
              <div class="flex flex-col space-y-4">
                <h2 class="text-base font-medium leading-tight">DAC</h2>
                <p class="text-xs text-gray-600 leading-tight mt-[2px]">
                  04/24/2025
                </p>
                <h2 class="text-base font-medium leading-tight">CXB</h2>
              </div>
            </div>

            <!-- Price Normal -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 opacity-100 translate-x-0 transition-all duration-200 group-hover:opacity-0 group-hover:-translate-x-4 relative z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Price On Hover -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 absolute right-10 left-0 bottom-4 opacity-0 -translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-800 z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Bottom Border Animation -->
            <span
              class="absolute right-0 bottom-0 h-[1.5px] bg-orange-500 w-0 origin-right group-hover:w-full transition-all duration-300"
            ></span>
          </div>
          <div
            class="w-full rounded-md shadow-md dark:bg-gray-50 dark:text-gray-800 p-4 bg-white relative group transition-all duration-300 hover:-translate-y-1 hover:scale-110 overflow-hidden"
          >
            <!-- Circle Burst on Hover Only -->
            <div
              class="absolute -top-10 -right-10 w-32 h-32 opacity-0 scale-75 translate-x-4 -translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-0"
            >
              <!-- Outer Circle -->
              <div
                class="absolute top-1 right-1 w-24 h-24 bg-orange-100 rounded-full z-10"
              ></div>
              <!-- Middle Circle -->
              <div
                class="absolute top-4 right-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
              ></div>
              <!-- Inner Circle -->
              <div
                class="absolute top-6 right-6 w-12 h-12 bg-orange-600 rounded-full z-30"
              ></div>
            </div>

            <!-- Airline Logo -->
            <img
              src="https://www.travomint.com/resources/images/airline-logo/BS.png?w=1920"
              alt=""
              class="w-16 relative z-40"
            />

            <!-- Route Info -->
            <div class="flex items-stretch gap-2 mt-2 relative z-40">
              <!-- Dots and line -->
              <div class="flex flex-col justify-between items-center">
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
                <div class="flex-1 w-[1px] bg-gray-600"></div>
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
              </div>

              <!-- Airport codes and date -->
              <div class="flex flex-col space-y-4">
                <h2 class="text-base font-medium leading-tight">DAC</h2>
                <p class="text-xs text-gray-600 leading-tight mt-[2px]">
                  04/24/2025
                </p>
                <h2 class="text-base font-medium leading-tight">CXB</h2>
              </div>
            </div>

            <!-- Price Normal -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 opacity-100 translate-x-0 transition-all duration-200 group-hover:opacity-0 group-hover:-translate-x-4 relative z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Price On Hover -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 absolute right-10 left-0 bottom-4 opacity-0 -translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-800 z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Bottom Border Animation -->
            <span
              class="absolute right-0 bottom-0 h-[1.5px] bg-orange-500 w-0 origin-right group-hover:w-full transition-all duration-300"
            ></span>
          </div>
          <div
            class="w-full rounded-md shadow-md dark:bg-gray-50 dark:text-gray-800 p-4 bg-white relative group transition-all duration-300 hover:-translate-y-1 hover:scale-110 overflow-hidden"
          >
            <!-- Circle Burst on Hover Only -->
            <div
              class="absolute -top-10 -right-10 w-32 h-32 opacity-0 scale-75 translate-x-4 -translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-0"
            >
              <!-- Outer Circle -->
              <div
                class="absolute top-1 right-1 w-24 h-24 bg-orange-100 rounded-full z-10"
              ></div>
              <!-- Middle Circle -->
              <div
                class="absolute top-4 right-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
              ></div>
              <!-- Inner Circle -->
              <div
                class="absolute top-6 right-6 w-12 h-12 bg-orange-600 rounded-full z-30"
              ></div>
            </div>

            <!-- Airline Logo -->
            <img
              src="https://www.travomint.com/resources/images/airline-logo/BS.png?w=1920"
              alt=""
              class="w-16 relative z-40"
            />

            <!-- Route Info -->
            <div class="flex items-stretch gap-2 mt-2 relative z-40">
              <!-- Dots and line -->
              <div class="flex flex-col justify-between items-center">
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
                <div class="flex-1 w-[1px] bg-gray-600"></div>
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
              </div>

              <!-- Airport codes and date -->
              <div class="flex flex-col space-y-4">
                <h2 class="text-base font-medium leading-tight">DAC</h2>
                <p class="text-xs text-gray-600 leading-tight mt-[2px]">
                  04/24/2025
                </p>
                <h2 class="text-base font-medium leading-tight">CXB</h2>
              </div>
            </div>

            <!-- Price Normal -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 opacity-100 translate-x-0 transition-all duration-200 group-hover:opacity-0 group-hover:-translate-x-4 relative z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Price On Hover -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 absolute right-10 left-0 bottom-4 opacity-0 -translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-800 z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Bottom Border Animation -->
            <span
              class="absolute right-0 bottom-0 h-[1.5px] bg-orange-500 w-0 origin-right group-hover:w-full transition-all duration-300"
            ></span>
          </div>
          <div
            class="w-full rounded-md shadow-md dark:bg-gray-50 dark:text-gray-800 p-4 bg-white relative group transition-all duration-300 hover:-translate-y-1 hover:scale-110 overflow-hidden"
          >
            <!-- Circle Burst on Hover Only -->
            <div
              class="absolute -top-10 -right-10 w-32 h-32 opacity-0 scale-75 translate-x-4 -translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-0"
            >
              <!-- Outer Circle -->
              <div
                class="absolute top-1 right-1 w-24 h-24 bg-orange-100 rounded-full z-10"
              ></div>
              <!-- Middle Circle -->
              <div
                class="absolute top-4 right-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
              ></div>
              <!-- Inner Circle -->
              <div
                class="absolute top-6 right-6 w-12 h-12 bg-orange-600 rounded-full z-30"
              ></div>
            </div>

            <!-- Airline Logo -->
            <img
              src="https://www.travomint.com/resources/images/airline-logo/BS.png?w=1920"
              alt=""
              class="w-16 relative z-40"
            />

            <!-- Route Info -->
            <div class="flex items-stretch gap-2 mt-2 relative z-40">
              <!-- Dots and line -->
              <div class="flex flex-col justify-between items-center">
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
                <div class="flex-1 w-[1px] bg-gray-600"></div>
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
              </div>

              <!-- Airport codes and date -->
              <div class="flex flex-col space-y-4">
                <h2 class="text-base font-medium leading-tight">DAC</h2>
                <p class="text-xs text-gray-600 leading-tight mt-[2px]">
                  04/24/2025
                </p>
                <h2 class="text-base font-medium leading-tight">CXB</h2>
              </div>
            </div>

            <!-- Price Normal -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 opacity-100 translate-x-0 transition-all duration-200 group-hover:opacity-0 group-hover:-translate-x-4 relative z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Price On Hover -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 absolute right-10 left-0 bottom-4 opacity-0 -translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-800 z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Bottom Border Animation -->
            <span
              class="absolute right-0 bottom-0 h-[1.5px] bg-orange-500 w-0 origin-right group-hover:w-full transition-all duration-300"
            ></span>
          </div>
          <div
            class="w-full rounded-md shadow-md dark:bg-gray-50 dark:text-gray-800 p-4 bg-white relative group transition-all duration-300 hover:-translate-y-1 hover:scale-110 overflow-hidden"
          >
            <!-- Circle Burst on Hover Only -->
            <div
              class="absolute -top-10 -right-10 w-32 h-32 opacity-0 scale-75 translate-x-4 -translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-0"
            >
              <!-- Outer Circle -->
              <div
                class="absolute top-1 right-1 w-24 h-24 bg-orange-100 rounded-full z-10"
              ></div>
              <!-- Middle Circle -->
              <div
                class="absolute top-4 right-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
              ></div>
              <!-- Inner Circle -->
              <div
                class="absolute top-6 right-6 w-12 h-12 bg-orange-600 rounded-full z-30"
              ></div>
            </div>

            <!-- Airline Logo -->
            <img
              src="https://www.travomint.com/resources/images/airline-logo/BS.png?w=1920"
              alt=""
              class="w-16 relative z-40"
            />

            <!-- Route Info -->
            <div class="flex items-stretch gap-2 mt-2 relative z-40">
              <!-- Dots and line -->
              <div class="flex flex-col justify-between items-center">
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
                <div class="flex-1 w-[1px] bg-gray-600"></div>
                <div
                  class="h-2 w-2 rounded-full bg-slate-600 group-hover:bg-orange-600 transition-colors duration-300"
                ></div>
              </div>

              <!-- Airport codes and date -->
              <div class="flex flex-col space-y-4">
                <h2 class="text-base font-medium leading-tight">DAC</h2>
                <p class="text-xs text-gray-600 leading-tight mt-[2px]">
                  04/24/2025
                </p>
                <h2 class="text-base font-medium leading-tight">CXB</h2>
              </div>
            </div>

            <!-- Price Normal -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 opacity-100 translate-x-0 transition-all duration-200 group-hover:opacity-0 group-hover:-translate-x-4 relative z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Price On Hover -->
            <p
              class="text-lg font-semibold text-orange-700 mt-4 absolute right-10 left-0 bottom-4 opacity-0 -translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-800 z-40"
            >
              <span class="text-lg font-semibold">৳</span> 6,609
            </p>

            <!-- Bottom Border Animation -->
            <span
              class="absolute right-0 bottom-0 h-[1.5px] bg-orange-500 w-0 origin-right group-hover:w-full transition-all duration-300"
            ></span>
          </div>
        </div>
      </div>
    </section>

    <!-- International Routes Section -->
    <section class="px-20 my-10">
      <div class="flex justify-between mb-4">
        <h2 class="text-2xl font-semibold text-slate-950 mb-5">
          International Routes
        </h2>
        <div class="flex gap-1">
          <button
            class="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center"
          >
            <span class="ml-1 text-xs py-1"> International </span>
          </button>
          <button
            class="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center"
          >
            <span class="ml-1 text-xs py-1"> Domestic </span>
          </button>
        </div>
      </div>
      <div class="grid grid-cols-6 gap-4">
        <div
          class="w-full max-w-[220px] relative rounded-md shadow-md bg-white flex flex-col items-center text-center py-6 px-6 group transition-all duration-500"
        >
          <!-- Border Animation (4 edges) -->
          <span
            class="absolute top-0 left-0 w-full h-full rounded-md border-2 border-transparent group-hover:border-orange-500 transition-all duration-500 ease-in-out pointer-events-none"
          ></span>

          <!-- Flipping Image -->
          <div class="w-24 h-24 relative">
            <div class="relative w-full h-full">
              <img
                src="https://t3.ftcdn.net/jpg/03/04/88/18/360_F_304881889_yJ1S3butl9gVs0kMptYTU2N1EVmEJbz8.jpg"
                alt="Route"
                class="rounded-full w-full h-full object-cover transform group-hover:rotate-y-180 transition-transform duration-300"
              />
            </div>
          </div>

          <!-- Card Info -->
          <div class="mt-4">
            <p class="text-xs text-gray-600">04/12/2025</p>

            <div class="flex justify-center gap-4 mt-3 items-center">
              <h2 class="text-base font-medium">DAC</h2>
              <h2 class="text-orange-600 text-4xl -mt-3">⇌</h2>
              <h2 class="text-base font-medium">DOH</h2>
            </div>

            <p class="text-xs text-orange-700 py-2">Premium Economy</p>

            <p class="text-lg font-semibold text-black py-3">
              <span class="text-lg font-semibold">৳</span> 92,518
            </p>
          </div>
        </div>
        <div
          class="w-full max-w-[220px] relative rounded-md shadow-md bg-white flex flex-col items-center text-center py-6 px-6 group transition-all duration-500"
        >
          <!-- Border Animation (4 edges) -->
          <span
            class="absolute top-0 left-0 w-full h-full rounded-md border-2 border-transparent group-hover:border-orange-500 transition-all duration-500 ease-in-out pointer-events-none"
          ></span>

          <!-- Flipping Image -->
          <div class="w-24 h-24 relative">
            <div class="relative w-full h-full">
              <img
                src="https://t3.ftcdn.net/jpg/03/04/88/18/360_F_304881889_yJ1S3butl9gVs0kMptYTU2N1EVmEJbz8.jpg"
                alt="Route"
                class="rounded-full w-full h-full object-cover transform group-hover:rotate-y-180 transition-transform duration-300"
              />
            </div>
          </div>

          <!-- Card Info -->
          <div class="mt-4">
            <p class="text-xs text-gray-600">04/12/2025</p>

            <div class="flex justify-center gap-4 mt-3 items-center">
              <h2 class="text-base font-medium">DAC</h2>
              <h2 class="text-orange-600 text-4xl -mt-3">⇌</h2>
              <h2 class="text-base font-medium">DOH</h2>
            </div>

            <p class="text-xs text-orange-700 py-2">Premium Economy</p>

            <p class="text-lg font-semibold text-black py-3">
              <span class="text-lg font-semibold">৳</span> 92,518
            </p>
          </div>
        </div>
        <div
          class="w-full max-w-[220px] relative rounded-md shadow-md bg-white flex flex-col items-center text-center py-6 px-6 group transition-all duration-500"
        >
          <!-- Border Animation (4 edges) -->
          <span
            class="absolute top-0 left-0 w-full h-full rounded-md border-2 border-transparent group-hover:border-orange-500 transition-all duration-500 ease-in-out pointer-events-none"
          ></span>

          <!-- Flipping Image -->
          <div class="w-24 h-24 relative">
            <div class="relative w-full h-full">
              <img
                src="https://t3.ftcdn.net/jpg/03/04/88/18/360_F_304881889_yJ1S3butl9gVs0kMptYTU2N1EVmEJbz8.jpg"
                alt="Route"
                class="rounded-full w-full h-full object-cover transform group-hover:rotate-y-180 transition-transform duration-300"
              />
            </div>
          </div>

          <!-- Card Info -->
          <div class="mt-4">
            <p class="text-xs text-gray-600">04/12/2025</p>

            <div class="flex justify-center gap-4 mt-3 items-center">
              <h2 class="text-base font-medium">DAC</h2>
              <h2 class="text-orange-600 text-4xl -mt-3">⇌</h2>
              <h2 class="text-base font-medium">DOH</h2>
            </div>

            <p class="text-xs text-orange-700 py-2">Premium Economy</p>

            <p class="text-lg font-semibold text-black py-3">
              <span class="text-lg font-semibold">৳</span> 92,518
            </p>
          </div>
        </div>
        <div
          class="w-full max-w-[220px] relative rounded-md shadow-md bg-white flex flex-col items-center text-center py-6 px-6 group transition-all duration-500"
        >
          <!-- Border Animation (4 edges) -->
          <span
            class="absolute top-0 left-0 w-full h-full rounded-md border-2 border-transparent group-hover:border-orange-500 transition-all duration-500 ease-in-out pointer-events-none"
          ></span>

          <!-- Flipping Image -->
          <div class="w-24 h-24 relative">
            <div class="relative w-full h-full">
              <img
                src="https://t3.ftcdn.net/jpg/03/04/88/18/360_F_304881889_yJ1S3butl9gVs0kMptYTU2N1EVmEJbz8.jpg"
                alt="Route"
                class="rounded-full w-full h-full object-cover transform group-hover:rotate-y-180 transition-transform duration-300"
              />
            </div>
          </div>

          <!-- Card Info -->
          <div class="mt-4">
            <p class="text-xs text-gray-600">04/12/2025</p>

            <div class="flex justify-center gap-4 mt-3 items-center">
              <h2 class="text-base font-medium">DAC</h2>
              <h2 class="text-orange-600 text-4xl -mt-3">⇌</h2>
              <h2 class="text-base font-medium">DOH</h2>
            </div>

            <p class="text-xs text-orange-700 py-2">Premium Economy</p>

            <p class="text-lg font-semibold text-black py-3">
              <span class="text-lg font-semibold">৳</span> 92,518
            </p>
          </div>
        </div>
        <div
          class="w-full max-w-[220px] relative rounded-md shadow-md bg-white flex flex-col items-center text-center py-6 px-6 group transition-all duration-500"
        >
          <!-- Border Animation (4 edges) -->
          <span
            class="absolute top-0 left-0 w-full h-full rounded-md border-2 border-transparent group-hover:border-orange-500 transition-all duration-500 ease-in-out pointer-events-none"
          ></span>

          <!-- Flipping Image -->
          <div class="w-24 h-24 relative">
            <div class="relative w-full h-full">
              <img
                src="https://t3.ftcdn.net/jpg/03/04/88/18/360_F_304881889_yJ1S3butl9gVs0kMptYTU2N1EVmEJbz8.jpg"
                alt="Route"
                class="rounded-full w-full h-full object-cover transform group-hover:rotate-y-180 transition-transform duration-300"
              />
            </div>
          </div>

          <!-- Card Info -->
          <div class="mt-4">
            <p class="text-xs text-gray-600">04/12/2025</p>

            <div class="flex justify-center gap-4 mt-3 items-center">
              <h2 class="text-base font-medium">DAC</h2>
              <h2 class="text-orange-600 text-4xl -mt-3">⇌</h2>
              <h2 class="text-base font-medium">DOH</h2>
            </div>

            <p class="text-xs text-orange-700 py-2">Premium Economy</p>

            <p class="text-lg font-semibold text-black py-3">
              <span class="text-lg font-semibold">৳</span> 92,518
            </p>
          </div>
        </div>
        <div
          class="w-full max-w-[220px] relative rounded-md shadow-md bg-white flex flex-col items-center text-center py-6 px-6 group transition-all duration-500"
        >
          <!-- Border Animation (4 edges) -->
          <span
            class="absolute top-0 left-0 w-full h-full rounded-md border-2 border-transparent group-hover:border-orange-500 transition-all duration-500 ease-in-out pointer-events-none"
          ></span>

          <!-- Flipping Image -->
          <div class="w-24 h-24 relative">
            <div class="relative w-full h-full">
              <img
                src="https://t3.ftcdn.net/jpg/03/04/88/18/360_F_304881889_yJ1S3butl9gVs0kMptYTU2N1EVmEJbz8.jpg"
                alt="Route"
                class="rounded-full w-full h-full object-cover transform group-hover:rotate-y-180 transition-transform duration-300"
              />
            </div>
          </div>

          <!-- Card Info -->
          <div class="mt-4">
            <p class="text-xs text-gray-600">04/12/2025</p>

            <div class="flex justify-center gap-4 mt-3 items-center">
              <h2 class="text-base font-medium">DAC</h2>
              <h2 class="text-orange-600 text-4xl -mt-3">⇌</h2>
              <h2 class="text-base font-medium">DOH</h2>
            </div>

            <p class="text-xs text-orange-700 py-2">Premium Economy</p>

            <p class="text-lg font-semibold text-black py-3">
              <span class="text-lg font-semibold">৳</span> 92,518
            </p>
          </div>
        </div>
      </div>
    </section>
    <!-- Articles Section -->
    <section class="relative pb-10">
      <div class="h-80 bg-pink-100 relative overflow-hidden">
        <svg
          class="absolute top-0 left-0 w-full"
          viewBox="0 0 1440 100"
          preserveAspectRatio="none"
        >
          <path
            d="M0,0 C360,100 1080,100 1440,0 L1440,0 L0,0 Z"
            fill="#ffffff"
          />
        </svg>
      </div>
      <div class="relative z-10 -mt-48 px-20">
        <div class="flex justify-between mb-4">
          <h2 class="text-2xl font-semibold text-slate-950">Articles</h2>
          <h2 class="text-orange-600 text-lg">View All ></h2>
        </div>
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- Repeatable Card -->
          <div class="w-full rounded-md relative group">
            <!-- Image Container (fixed position, no scale) -->
            <div class="w-full rounded-md relative overflow-hidden h-[230px]">
              <img
                src="https://img.freepik.com/premium-photo/illustration-travel-concept-with-plane-famous-landmark-world-traveling-luggage_1028938-338088.jpg"
                alt=""
                class="w-full h-full object-cover transition-transform duration-300 ease-out group-hover:scale-125"
              />

              <!-- Circle Burst on Hover - BOTTOM LEFT -->
              <div
                class="absolute -bottom-10 -left-10 w-32 h-32 opacity-0 scale-75 -translate-x-4 translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-30"
              >
                <!-- Outer Circle -->
                <div
                  class="absolute bottom-1 left-1 w-24 h-24 bg-orange-100 rounded-full z-10"
                ></div>
                <!-- Middle Circle -->
                <div
                  class="absolute bottom-4 left-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
                ></div>
                <!-- Inner Circle -->
                <div
                  class="absolute bottom-6 left-6 w-12 h-12 bg-orange-600 rounded-full z-30"
                ></div>
              </div>
            </div>

            <!-- Text Content -->
            <div class="text-base font-semibold text-zinc-800 mt-4 space-y-2">
              <p>
                <i class="fa-solid fa-calendar"></i>
                <span class="px-1">Apr 09, 2025</span>
              </p>
              <p class="group-hover:text-orange-600">
                Lorem ipsum dolor sit amet sit adipisicing amet elit?
              </p>
            </div>
          </div>
          <div class="w-full rounded-md relative group">
            <!-- Image Container (fixed position, no scale) -->
            <div class="w-full rounded-md relative overflow-hidden h-[230px]">
              <img
                src="https://img.freepik.com/premium-photo/illustration-travel-concept-with-plane-famous-landmark-world-traveling-luggage_1028938-338088.jpg"
                alt=""
                class="w-full h-full object-cover transition-transform duration-300 ease-out group-hover:scale-125"
              />

              <!-- Circle Burst on Hover - BOTTOM LEFT -->
              <div
                class="absolute -bottom-10 -left-10 w-32 h-32 opacity-0 scale-75 -translate-x-4 translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-30"
              >
                <!-- Outer Circle -->
                <div
                  class="absolute bottom-1 left-1 w-24 h-24 bg-orange-100 rounded-full z-10"
                ></div>
                <!-- Middle Circle -->
                <div
                  class="absolute bottom-4 left-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
                ></div>
                <!-- Inner Circle -->
                <div
                  class="absolute bottom-6 left-6 w-12 h-12 bg-orange-600 rounded-full z-30"
                ></div>
              </div>
            </div>

            <!-- Text Content -->
            <div class="text-base font-semibold text-zinc-800 mt-4 space-y-2">
              <p>
                <i class="fa-solid fa-calendar"></i>
                <span class="px-1">Apr 09, 2025</span>
              </p>
              <p class="group-hover:text-orange-600">
                Lorem ipsum dolor sit amet sit adipisicing amet elit?
              </p>
            </div>
          </div>
          <div class="w-full rounded-md relative group">
            <!-- Image Container (fixed position, no scale) -->
            <div class="w-full rounded-md relative overflow-hidden h-[230px]">
              <img
                src="https://img.freepik.com/premium-photo/illustration-travel-concept-with-plane-famous-landmark-world-traveling-luggage_1028938-338088.jpg"
                alt=""
                class="w-full h-full object-cover transition-transform duration-300 ease-out group-hover:scale-125"
              />

              <!-- Circle Burst on Hover - BOTTOM LEFT -->
              <div
                class="absolute -bottom-10 -left-10 w-32 h-32 opacity-0 scale-75 -translate-x-4 translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-30"
              >
                <!-- Outer Circle -->
                <div
                  class="absolute bottom-1 left-1 w-24 h-24 bg-orange-100 rounded-full z-10"
                ></div>
                <!-- Middle Circle -->
                <div
                  class="absolute bottom-4 left-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
                ></div>
                <!-- Inner Circle -->
                <div
                  class="absolute bottom-6 left-6 w-12 h-12 bg-orange-600 rounded-full z-30"
                ></div>
              </div>
            </div>

            <!-- Text Content -->
            <div class="text-base font-semibold text-zinc-800 mt-4 space-y-2">
              <p>
                <i class="fa-solid fa-calendar"></i>
                <span class="px-1">Apr 09, 2025</span>
              </p>
              <p class="group-hover:text-orange-600">
                Lorem ipsum dolor sit amet sit adipisicing amet elit?
              </p>
            </div>
          </div>
          <div class="w-full rounded-md relative group">
            <!-- Image Container (fixed position, no scale) -->
            <div class="w-full rounded-md relative overflow-hidden h-[230px]">
              <img
                src="https://img.freepik.com/premium-photo/illustration-travel-concept-with-plane-famous-landmark-world-traveling-luggage_1028938-338088.jpg"
                alt=""
                class="w-full h-full object-cover transition-transform duration-300 ease-out group-hover:scale-125"
              />

              <!-- Circle Burst on Hover - BOTTOM LEFT -->
              <div
                class="absolute -bottom-10 -left-10 w-32 h-32 opacity-0 scale-75 -translate-x-4 translate-y-4 group-hover:opacity-100 group-hover:scale-100 group-hover:translate-x-0 group-hover:translate-y-0 transition-all duration-300 ease-out z-30"
              >
                <!-- Outer Circle -->
                <div
                  class="absolute bottom-1 left-1 w-24 h-24 bg-orange-100 rounded-full z-10"
                ></div>
                <!-- Middle Circle -->
                <div
                  class="absolute bottom-4 left-4 w-[70px] h-[70px] bg-orange-300 rounded-full z-20"
                ></div>
                <!-- Inner Circle -->
                <div
                  class="absolute bottom-6 left-6 w-12 h-12 bg-orange-600 rounded-full z-30"
                ></div>
              </div>
            </div>

            <!-- Text Content -->
            <div class="text-base font-semibold text-zinc-800 mt-4 space-y-2">
              <p>
                <i class="fa-solid fa-calendar"></i>
                <span class="px-1">Apr 09, 2025</span>
              </p>
              <p class="group-hover:text-orange-600">
                Lorem ipsum dolor sit amet sit adipisicing amet elit?
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="mt-10">
      <div
        class="relative flex w-full items-center justify-center overflow-hidden bg-white py-10"
      >
        <!-- Dot Background -->
        <div
          class="absolute inset-0 bg-[radial-gradient(black_1.2px,transparent_1.2px)] bg-[length:20px_20px] opacity-40"
        ></div>

        <!-- Content -->
        <div class="z-10 w-full">
          <div class="flex justify-evenly flex-wrap gap-6 w-full px-4">
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/iata.png"
              alt=""
            />
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/sabre.png"
              alt=""
            />
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/a3.png"
              alt=""
            />
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/a5.png"
              alt=""
            />
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/a2.png"
              alt=""
            />
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/c1.png"
              alt=""
            />
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/c2.png"
              alt=""
            />
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/sabre.png"
              alt=""
            />
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/sabre.png"
              alt=""
            />
            <img
              class="w-28 h-w-28 bg-white shadow-lg p-4 rounded-md"
              src="https://www.travomint.com/Image/sabre.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer
      class="bg-blue-950 text-white px-20 py-16 grid md:grid-cols-4 gap-10"
    >
      <!-- About -->
      <nav>
        <h6 class="footer-titles font-semibold text-base mb-6">About</h6>
        <ul class="space-y-2 mt-4">
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> About Us
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Article
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Blog
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Media
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> FAQ
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Contact Us
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Business Class
          </li>
        </ul>
      </nav>

      <!-- Bookings -->
      <nav>
        <h6 class="footer-titles font-semibold text-base mb-6">Bookings</h6>
        <ul class="space-y-2 mt-4">
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Flights
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Hotels Booking
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Car Rental
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Transfers
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Vacation
          </li>
        </ul>
      </nav>

      <!-- Other Links -->
      <nav>
        <h6 class="footer-titles font-semibold text-base mb-6">Other Links</h6>
        <ul class="space-y-2 mt-4">
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Airlines
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Español
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> International
            Flights
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Visa
          </li>
          <li
            class="flex items-center gap-2 text-sm hover:text-orange-400 cursor-pointer"
          >
            <span class="text-orange-500 text-xl">&rsaquo;</span> Forum
          </li>
        </ul>
      </nav>

      <!-- Connect With Us -->
      <div>
        <h6 class="footer-titles font-semibold text-base mb-6">
          Connect With Us
        </h6>

        <!-- Email Subscription -->
        <div class="relative w-full max-w-xs mb-4">
          <input
            type="email"
            placeholder="Enter your email address"
            class="input input-bordered text-black w-full pr-12"
          />
          <button
            class="absolute right-2 top-1/2 -translate-y-1/2 bg-base-200 hover:bg-base-300 text-gray-600 p-2 px-3 rounded"
          >
            <i class="fa-solid fa-paper-plane"></i>
          </button>
        </div>

        <!-- Store Badges -->
        <div class="flex gap-2 -mt-10">
          <img
            src="https://www.svgrepo.com/show/303128/download-on-the-app-store-apple-logo.svg"
            alt="App Store"
            class="w-32 h-40 object-contain"
          />
          <img
            src="https://www.svgrepo.com/show/303139/google-play-badge-logo.svg"
            alt="Google Play"
            class="w-32 h-40 object-contain"
          />
        </div>

        <!-- Social Icons -->
        <div class="flex gap-3 -mt-10">
          <i
            class="fa-brands fa-facebook text-xl text-white bg-blue-700 hover:bg-orange-400 cursor-pointer px-2 py-1 rounded-lg"
          ></i>
          <i
            class="fa-brands fa-instagram text-xl text-white bg-pink-500 hover:bg-orange-400 cursor-pointer px-2 py-1 rounded-lg"
          ></i>
          <i
            class="fa-brands fa-x-twitter text-xl text-white bg-black hover:bg-orange-400 cursor-pointer px-2 py-1 rounded-lg"
          ></i>
          <i
            class="fa-brands fa-pinterest text-xl text-white bg-red-600 hover:bg-orange-400 cursor-pointer px-2 py-1 rounded-lg"
          ></i>
          <i
            class="fa-brands fa-linkedin text-xl text-white bg-blue-600 hover:bg-orange-400 cursor-pointer px-2 py-1 rounded-lg"
          ></i>
        </div>
      </div>
    </footer>

    <!-- Footer Bottom -->
    <div class="bg-blue-950 px-20 text-sm -mt-16 space-y-4 pb-8">
      <p class="border-t border-gray-700"></p>
      <p class="text-slate-400 hover:text-orange-800 font-medium">
        <span class="text-white font-semibold">Top International Routes </span
        >Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias cum
        nobis quisquam molestias provident exercitationem autem culpa eum
        excepturi modi repudiandae, voluptate at accusamus, adipisci veniam
        sapiente magni, perspiciatis reprehenderit?Lorem ipsum dolor sit amet
        consectetur adipisicing elit.
      </p>
      <p class="border-t border-gray-700"></p>
      <p class="text-slate-400 hover:text-orange-800 font-medium">
        <span class="text-white font-semibold">Airlines </span>Lorem ipsum dolor
        sit amet, consectetur adipisicing elit. Alias cum nobis quisquam
        molesti.
      </p>
      <p class="border-t border-gray-700"></p>
      <p class="text-slate-400 hover:text-orange-800 font-medium">
        <span class="text-white font-semibold">Airports </span>Lorem ipsum dolor
        sit amet, consectetur adipisicing elit. Alias cum nobis quisquam
        molestias provident exercitationem autem culpa eum excepturi modi
        repudiandae, voluptate at accusamus, adipisci veniam sapiente magni,
        perspiciatis reprehenderit?Lorem ipsum dolor sit amet consectetur
        adipisicing elit
      </p>
      <p class="border-t border-gray-300"></p>
      <div class="grid grid-cols-1 lg:grid-cols-3">
        <p class="text-white text-center">(Part of SNVA)</p>

        <div>
          <h2 class="text-4xl font-semibold text-orange-600 text-center">
            Travomint
          </h2>
          <p class="text-white text-center mt-2">
            &copy; 2025 TravelGo. All rights reserved.
          </p>
        </div>

        <div class="flex gap-4 text-white px-14 lg:px-0">
          <a href="#" class="hover:text-orange-400 transition-colors"
            >Privacy Policy</a
          >
          <a href="#" class="hover:text-orange-400 transition-colors"
            >Terms & Conditions</a
          >
        </div>
      </div>
      <div
        class="bg-slate-900 w-full mt-10 rounded-lg text-white lg:flex justify-center items-center gap-5 p-4 lg:p-6 text-center"
      >
        <p>
          <i class="fa-solid fa-phone-volume text-orange-600"></i> Number : +
          018186107893
        </p>
        <p class="text-sm text-slate-400">or , simply</p>
        <p>
          <i class="fa-solid fa-envelope text-orange-600"> </i> Email :
          care@travomint,com
        </p>
      </div>
    </div>
    <div class="flex justify-center items-center gap-2 lg:gap-10 py-5">
      <img
        src="https://www.travomint.com/_next/image?url=%2FImage%2Fiata12.png&w=256&q=75"
        alt=""
      />
      <img
        src="https://www.travomint.com/_next/image?url=%2FImage%2Fveri12.png&w=256&q=75"
        alt=""
      />
      <img
        src="https://www.travomint.com/_next/image?url=%2FImage%2Fmas12.png&w=128&q=75"
        alt=""
      />
      <img
        src="https://www.travomint.com/_next/image?url=%2FImage%2Fvisa12.png&w=256&q=75"
        alt=""
      />
    </div>
  </body>
</html>
