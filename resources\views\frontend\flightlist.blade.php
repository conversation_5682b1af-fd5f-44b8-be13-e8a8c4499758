<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Albaraka - Travel Booking</title>
    {{-- <link href="https://cdn.jsdelivr.net/npm/daisyui@4.6.0/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script> --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">



    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.option-header').forEach(header => {
                header.addEventListener('click', function() {
                    // Look for the next sibling `.option-details` instead of querying inside
                    const details = this.nextElementSibling;

                    if (details && details.classList.contains('option-details')) {
                        details.classList.toggle('hidden');

                        // Rotate chevron icon
                        const icon = this.querySelector('.price-details-toggle i');
                        icon.classList.toggle('fa-chevron-down');
                        icon.classList.toggle('fa-chevron-up');
                    }
                });
            });
        });
    </script>


    <!-- ✅ Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
        integrity="sha512-..." crossorigin="anonymous" referrerpolicy="no-referrer" />


</head>

<body class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-8">
                    <h1 class="text-2xl font-bold text-orange-600">Albaraka</h1>
                    <nav class="hidden md:flex space-x-6">
                        <a href="#" class="flex items-center text-gray-600 hover:text-orange-600">
                            <i data-lucide="plane" class="w-4 h-4 mr-1"></i> Flights
                        </a>
                        <a href="#" class="flex items-center text-gray-600 hover:text-orange-600">
                            <i data-lucide="hotel" class="w-4 h-4 mr-1"></i> Hotels
                        </a>
                        <a href="#" class="flex items-center text-gray-600 hover:text-orange-600">
                            <i data-lucide="car" class="w-4 h-4 mr-1"></i> Car Rental
                        </a>
                    </nav>
                </div>
                <a href="#" class="bg-green-600 text-white px-4 py-2 hover:bg-orange-700">
                    Sign In
                </a>
            </div>
        </div>
    </header>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.option-header').forEach(header => {
                header.addEventListener('click', function() {
                    const flightOption = this.closest('.flight-option'); // safest way to scope
                    const details = flightOption.querySelector('.option-details');

                    if (details) {
                        details.classList.toggle('hidden');

                        // Toggle icon rotation
                        const icon = this.querySelector('.price-details-toggle i');
                        if (icon) {
                            icon.classList.toggle('fa-chevron-down');
                            icon.classList.toggle('fa-chevron-up');
                        }
                    }
                });
            });
        });
    </script>

<div class="container mx-auto px-4 py-8">

    <div class="flight-search-results space-y-6 p-6">
        <h1 class="text-2xl font-semibold text-gray-800">Available Flights</h1>

        @foreach ($pricingSolutions as $solution)
            <form action="{{ route('flights.booking') }}" method="POST"
                class="bg-white shadow-md rounded-2xl p-4 space-y-4">
                @csrf
                <input type="hidden" name="passengercount" value="{{ $passengercount }}">
                <!-- Header Section -->
                <div class="flight-option rounded-xl p-4 mb-4">
                <div
                    class="flex flex-col lg:flex-row justify-between items-center option-header cursor-pointer bg-white px-4 py-3 rounded-xl shadow-md border border-gray-200 hover:bg-gray-50 transition">
                    <div class="text-xl font-bold text-blue-600">
                        {{ $solution['currency'] }} {{ number_format((float) substr($solution['totalPrice'], 3), 2) }}
                        <span class="ml-2 text-sm text-gray-500 price-details-toggle">Details <i
                                class="fas fa-chevron-down transition-transform"></i></span>
                    </div>
                    <div class="text-sm text-gray-600 flex items-center gap-2 mt-2 lg:mt-0">
                        <i class="far fa-clock"></i> {{ $solution['travelTime'] }}
                    </div>
                    <div class="text-sm text-gray-600 mt-2 lg:mt-0">
                        {{ $solution['airline'] }} · {{ count($solution['segments']) }}
                        {{ count($solution['segments']) > 1 ? 'Segments' : 'Segment' }}
                    </div>
                </div>

                <!-- Hidden Input for Solution Data -->

                <input type="hidden" name="solution_data" value="{{ json_encode($solution) }}">
                <button type="submit"
                    class="mt-4 w-full bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition">
                    <i class="fas fa-ticket-alt"></i> Select Now
                </button>

                <div class="option-details hidden bg-gray-50 p-4 mt-4 rounded-xl">
                    @foreach ($solution['segments'] as $segment)
                        <div class="border border-gray-200 rounded-xl p-4 space-y-2">
                            <div class="flex justify-between items-center">
                                <div class="flex flex-col">
                                    <span class="font-semibold text-gray-800">{{ $segment['carrier_name'] }}</span>
                                    <span class="text-sm text-gray-600">Flight {{ $segment['flightNumber'] }}</span>
                                </div>

                                @if (isset($solution['fareInfo'][0]['baggage']))
                                    <div class="text-sm text-gray-600">
                                        <i class="fas fa-suitcase"></i>
                                        Checked:
                                        {{ $solution['fareInfo'][0]['baggage']['value'] }}{{ $solution['fareInfo'][0]['baggage']['unit'] }}
                                    </div>
                                @endif
                            </div>

                            <div class="grid grid-cols-3 text-sm text-gray-700">
                                <div class="space-y-1">
                                    <div class="font-bold">{{ $segment['departure']['time'] }}</div>
                                    <div>{{ $segment['departure']['date'] }}</div>
                                    <div>{{ $segment['origin'] }} Terminal {{ $segment['departure']['terminal'] }}
                                    </div>
                                </div>

                                <div class="flex flex-col items-center justify-center space-y-1">
                                    <div><i class="fas fa-clock"></i> {{ $segment['duration'] }}</div>
                                    <div>
                                        {{ $segment['numberofstops'] > 0 ? $segment['numberofstops'] . ' stop(s)' : 'Non-stop' }}
                                    </div>
                                    <div><i class="fas fa-plane"></i> {{ $segment['equipment'] }}</div>
                                </div>

                                <div class="space-y-1 text-right">
                                    <div class="font-bold">{{ $segment['arrival']['time'] }}</div>
                                    <div>{{ $segment['arrival']['date'] }}</div>
                                    <div>{{ $segment['destination'] }} Terminal {{ $segment['arrival']['terminal'] }}
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-between items-center pt-2 border-t text-sm text-gray-600">
                                @if ($segment['equipment'] === '388')
                                    <div><i class="fas fa-wifi"></i> WiFi Available</div>
                                @endif
                                <div><i class="fas fa-chair"></i> {{ $solution['cabinClass'] }}</div>
                            </div>
                        </div>
                    @endforeach

                    @if (isset($solution['fareInfo'][0]['baggage']))
                        <div class="bg-white p-4 rounded-lg border">
                            <h4 class="font-semibold text-gray-800 mb-2">Baggage Allowance</h4>
                            <ul class="list-disc list-inside text-sm text-gray-700">
                                <li>
                                    <i class="fas fa-suitcase"></i> Checked baggage:
                                    {{ $solution['fareInfo'][0]['baggage']['value'] }}{{ $solution['fareInfo'][0]['baggage']['unit'] }}
                                    per passenger
                                </li>
                                <li>
                                    <i class="fas fa-briefcase"></i> Carry-on: 1 piece (7kg max)
                                </li>
                            </ul>
                        </div>
                    @endif

                    <div class="flex gap-4 text-sm text-gray-700">
                        <span class="bg-gray-100 px-2 py-1 rounded-lg">Class: {{ $solution['bookingClass'] }}</span>
                        <span class="bg-gray-100 px-2 py-1 rounded-lg">Cabin: {{ $solution['cabinClass'] }}</span>
                        <span class="bg-gray-100 px-2 py-1 rounded-lg">Seats: {{ $solution['seatsAvailable'] }}</span>
                    </div>

                    <div class="pt-4">
                        <h4 class="font-semibold text-gray-800">Price Breakdown</h4>
                        <div class="text-sm text-gray-700 space-y-1">
                            <div class="flex justify-between">
                                <span>Base Fare:</span>
                                <span>{{ $solution['currency'] }}
                                    {{ number_format((float) substr($solution['basePrice'], 3), 2) }}</span>
                            </div>

                            @foreach ($solution['taxes'] as $tax)
                                <div class="flex justify-between">
                                    <span>Tax ({{ $tax['category'] }}):</span>
                                    <span>{{ $solution['currency'] }}
                                        {{ number_format((float) substr($tax['amount'], 3), 2) }}</span>
                                </div>
                            @endforeach

                            <div class="flex justify-between font-semibold">
                                <span>Total Price:</span>
                                <span>{{ $solution['totalPrice'] }}</span>
                            </div>
                        </div>

                        <div class="mt-3 text-sm text-gray-600 space-y-1">
                            <div><strong>Change Penalty:</strong>
                                @if ($solution['changePenalty']['percentage'])
                                    {{ $solution['changePenalty']['percentage'] }}% of fare
                                @elseif($solution['changePenalty']['amount'])
                                    {{ $solution['currency'] }} {{ $solution['changePenalty']['amount'] }}
                                @else
                                    {{ $solution['changePenalty']['type'] }}
                                @endif
                            </div>
                            <div><strong>Cancel Penalty:</strong>
                                @if ($solution['cancelPenalty']['percentage'])
                                    {{ $solution['cancelPenalty']['percentage'] }}% of fare
                                @elseif($solution['cancelPenalty']['amount'])
                                    {{ $solution['currency'] }} {{ $solution['cancelPenalty']['amount'] }}
                                @else
                                    {{ $solution['cancelPenalty']['type'] }}
                                @endif
                            </div>
                            @if ($solution['ticketingTime'])
                                <div><strong>Ticket by:</strong> {{ $solution['ticketingTime'] }}</div>
                            @endif
                        </div>
                    </div>
                </div>
                </div>
                <!-- End of Flight Option -->
            </form>
        @endforeach
    </div>
</div>
