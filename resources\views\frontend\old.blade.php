<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gozayaan - Flight Search</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
        .trip-type-active {
            color: #2563EB;
            border-bottom: 2px solid #2563EB;
        }
        .flight-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
    </style>

<style>
    .flight-search-results {
        font-family: '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
        max-width: 900px;
        margin: 30px auto;
        padding: 0 20px;
    }

    .results-header {
        color: #2c3e50;
        text-align: center;
        margin-bottom: 30px;
        font-size: 28px;
    }

    .flight-option {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        margin-bottom: 25px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .option-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        cursor: pointer;
    }

    .option-header>div {
        flex: 1;
    }

    .price {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
    }

    .price-details-toggle {
        font-size: 14px;
        color: #3498db;
        margin-left: 15px;
        font-weight: normal;
    }

    .travel-time,
    .airline {
        text-align: center;
        color: #7f8c8d;
        font-size: 15px;
    }

    .travel-time i {
        margin-right: 5px;
    }

    .option-details {
        padding: 20px;
        display: none;
    }

    .flight-option.expanded .option-details {
        display: block;
    }

    .flight-segment {
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px dashed #e0e0e0;
    }

    .flight-segment:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .segment-header {
        margin-bottom: 15px;
    }

    .segment-header .airline {
        font-weight: 600;
        color: #2c3e50;
        margin-right: 15px;
    }

    .segment-header .cabin-class {
        background-color: #e8f5e9;
        color: #388e3c;
        padding: 3px 10px;
        border-radius: 4px;
        font-size: 13px;
    }

    .segment-timings {
        display: flex;
        justify-content: space-between;
    }

    .departure,
    .arrival {
        flex: 2;
    }

    .duration {
        flex: 1;
        text-align: center;
        padding: 0 15px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .time {
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
    }

    .date {
        color: #7f8c8d;
        margin: 5px 0;
        font-size: 14px;
    }

    .airport {
        font-size: 15px;
        color: #34495e;
    }

    .flight-duration {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .flight-distance {
        font-size: 13px;
        color: #7f8c8d;
    }

    .segment-footer {
        margin-top: 15px;
        font-size: 13px;
        color: #7f8c8d;
    }

    .pricing-details {
        margin-top: 30px;
        display: flex;
    }

    .price-breakdown {
        flex: 2;
        padding-right: 20px;
    }

    .penalty-info {
        flex: 1;
        padding-left: 20px;
        border-left: 1px solid #eee;
    }

    .breakdown-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px dashed #eee;
    }

    .breakdown-row.total {
        font-weight: 600;
        border-bottom: none;
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px solid #ddd;
    }

    .penalty {
        margin-bottom: 15px;
        font-size: 14px;
    }

    .ticketing-time {
        font-size: 14px;
        color: #e74c3c;
        margin-top: 20px;
    }

    @media (max-width: 768px) {
        .option-header {
            flex-direction: column;
            text-align: center;
        }

        .option-header>div {
            margin-bottom: 10px;
        }

        .segment-timings {
            flex-direction: column;
        }

        .departure,
        .arrival,
        .duration {
            margin-bottom: 15px;
            text-align: center;
        }

        .pricing-details {
            flex-direction: column;
        }

        .price-breakdown {
            padding-right: 0;
            margin-bottom: 20px;
        }

        .penalty-info {
            padding-left: 0;
            border-left: none;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
    }

    /* Add these new styles to your existing CSS */
    .cabin-class-badge {
        background: #4CAF50;
        color: white;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin-left: 10px;
        display: inline-flex;
        align-items: center;
    }

    .cabin-class-badge i {
        margin-right: 5px;
    }

    .seats-available {
        background: rgba(0, 0, 0, 0.1);
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin-left: 10px;
        display: inline-flex;
        align-items: center;
    }

    .seats-available i {
        margin-right: 5px;
        font-size: 0.7rem;
    }

    .baggage-info {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-top: 8px;
    }

    .baggage-text,
    .carry-on-baggage {
        background: #e3f2fd;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
    }

    .carry-on-baggage {
        background: #e8f5e9;
    }

    .baggage-text i,
    .carry-on-baggage i {
        margin-right: 5px;
    }

    .aircraft-feature {
        background: #f5f5f5;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
    }

    .aircraft-feature i {
        margin-right: 5px;
        color: #3498db;
    }
</style>

</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <h1 class="text-2xl font-bold text-blue-600">Gozayaan</h1>
                <nav class="hidden md:flex ml-10 space-x-6">
                    <a href="#" class="font-medium text-gray-700 hover:text-blue-600">Flights</a>
                    <a href="#" class="font-medium text-gray-700 hover:text-blue-600">Hotels</a>
                    <a href="#" class="font-medium text-gray-700 hover:text-blue-600">Holidays</a>
                    <a href="#" class="font-medium text-gray-700 hover:text-blue-600">Bus</a>
                </nav>
            </div>
            <div class="flex items-center space-x-4">
                <button class="px-4 py-2 text-gray-700 hover:text-blue-600">
                    <i class="far fa-bell"></i>
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Sign In
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
        <!-- Search Form -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <!-- Trip Type Toggle -->
            <div class="flex border-b">
                <button class="px-4 py-3 mr-6 trip-type-active font-medium">
                    <i class="fas fa-plane-departure mr-2"></i>Flights
                </button>
                <button class="px-4 py-3 mr-6 text-gray-500 font-medium">
                    <i class="fas fa-hotel mr-2"></i>Hotels
                </button>
                <button class="px-4 py-3 text-gray-500 font-medium">
                    <i class="fas fa-suitcase-rolling mr-2"></i>Holidays
                </button>
            </div>

            <!-- Flight Type -->
            <div class="flex mt-4 mb-6">
                <button class="px-4 py-2 mr-4 bg-blue-100 text-blue-600 font-medium rounded-md">
                    Round Trip
                </button>
                <button class="px-4 py-2 mr-4 text-gray-500 font-medium">
                    One Way
                </button>
                <button class="px-4 py-2 text-gray-500 font-medium">
                    Multi City
                </button>
            </div>

            <!-- Search Fields -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div>
                    <label class="block text-gray-700 font-medium mb-2">From</label>
                    <div class="relative">
                        <input type="text" placeholder="City or Airport" 
                               class="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-plane-departure absolute left-3 top-3.5 text-gray-400"></i>
                    </div>
                </div>
                <div>
                    <label class="block text-gray-700 font-medium mb-2">To</label>
                    <div class="relative">
                        <input type="text" placeholder="City or Airport" 
                               class="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-plane-arrival absolute left-3 top-3.5 text-gray-400"></i>
                    </div>
                </div>
                <div>
                    <label class="block text-gray-700 font-medium mb-2">Departure</label>
                    <div class="relative">
                        <input type="date" class="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="far fa-calendar-alt absolute left-3 top-3.5 text-gray-400"></i>
                    </div>
                </div>
                <div>
                    <label class="block text-gray-700 font-medium mb-2">Return</label>
                    <div class="relative">
                        <input type="date" class="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="far fa-calendar-alt absolute left-3 top-3.5 text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Passenger and Class -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                    <label class="block text-gray-700 font-medium mb-2">Passengers</label>
                    <select class="w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>1 Adult</option>
                        <option>2 Adults</option>
                        <option>1 Adult, 1 Child</option>
                    </select>
                </div>
                <div>
                    <label class="block text-gray-700 font-medium mb-2">Travel Class</label>
                    <select class="w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>Economy</option>
                        <option>Premium Economy</option>
                        <option>Business</option>
                        <option>First Class</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
                        Search Flights
                    </button>
                </div>
            </div>
        </div>

        <!-- Deals Section -->
        <div class="mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Today's Best Deals</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <img src="https://via.placeholder.com/400x200?text=DAC+to+CXB" alt="Flight Deal" class="w-full h-40 object-cover">
                    <div class="p-4">
                        <h3 class="font-bold text-gray-800 mb-1">Dhaka to Cox's Bazar</h3>
                        <p class="text-gray-600 text-sm mb-2">Starting from</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xl font-bold text-blue-600">৳5,499</span>
                            <button class="text-blue-600 text-sm font-medium">View Deal</button>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <img src="https://via.placeholder.com/400x200?text=DAC+to+JSR" alt="Flight Deal" class="w-full h-40 object-cover">
                    <div class="p-4">
                        <h3 class="font-bold text-gray-800 mb-1">Dhaka to Jessore</h3>
                        <p class="text-gray-600 text-sm mb-2">Starting from</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xl font-bold text-blue-600">৳4,299</span>
                            <button class="text-blue-600 text-sm font-medium">View Deal</button>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md overflow-hidden">
                    <img src="https://via.placeholder.com/400x200?text=DAC+to+ZYL" alt="Flight Deal" class="w-full h-40 object-cover">
                    <div class="p-4">
                        <h3 class="font-bold text-gray-800 mb-1">Dhaka to Sylhet</h3>
                        <p class="text-gray-600 text-sm mb-2">Starting from</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xl font-bold text-blue-600">৳6,199</span>
                            <button class="text-blue-600 text-sm font-medium">View Deal</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Popular Routes -->
        <div class="mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Popular Flight Routes</h2>
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-plane text-blue-500 mr-2"></i>
                            <span class="font-medium">DAC → CXB</span>
                        </div>
                        <p class="text-sm text-gray-600">Dhaka to Cox's Bazar</p>
                    </div>
                    <div class="p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-plane text-blue-500 mr-2"></i>
                            <span class="font-medium">DAC → JSR</span>
                        </div>
                        <p class="text-sm text-gray-600">Dhaka to Jessore</p>
                    </div>
                    <div class="p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-plane text-blue-500 mr-2"></i>
                            <span class="font-medium">DAC → ZYL</span>
                        </div>
                        <p class="text-sm text-gray-600">Dhaka to Sylhet</p>
                    </div>
                    <div class="p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-plane text-blue-500 mr-2"></i>
                            <span class="font-medium">DAC → CGP</span>
                        </div>
                        <p class="text-sm text-gray-600">Dhaka to Chittagong</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-bold mb-4">About Gozayaan</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white">About Us</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Careers</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Blog</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-bold mb-4">Help</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white">FAQs</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Privacy Policy</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-bold mb-4">Products</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white">Flights</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Hotels</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white">Holidays</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-bold mb-4">Download App</h3>
                    <div class="space-y-3">
                        <button class="flex items-center bg-black text-white px-3 py-2 rounded-lg w-full">
                            <i class="fab fa-apple text-2xl mr-2"></i>
                            <div class="text-left">
                                <div class="text-xs">Download on the</div>
                                <div class="font-medium">App Store</div>
                            </div>
                        </button>
                        <button class="flex items-center bg-black text-white px-3 py-2 rounded-lg w-full">
                            <i class="fab fa-google-play text-2xl mr-2"></i>
                            <div class="text-left">
                                <div class="text-xs">Get it on</div>
                                <div class="font-medium">Google Play</div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-6 text-center text-gray-400">
                <p>© 2023 Gozayaan. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>