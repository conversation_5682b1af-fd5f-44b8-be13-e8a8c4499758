<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class SabreAuthService
{

    protected $authUrl;

    public function __construct()
    {
        $this->authUrl = config('sabre.auth_url');
    }

    public function getAccessToken()
    {

        // return 'ok';
        return Cache::remember('sabre_rest_token', 1700, function () {

            $client = new Client([
                'base_uri' => 'https://api.cert.platform.sabre.com',
                'headers' => [
                    'Authorization' => 'Basic ' . env('SABRE_SECRET'),
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ]
            ]);

            try {
                $response = $client->post('/v2/auth/token', [
                    'form_params' => [
                        'grant_type' => 'client_credentials'
                    ]
                ]);

                if ($response->getStatusCode() == 200) {
                    $responseBody = json_decode($response->getBody(), true);
                    return $responseBody['access_token'];
                }

                throw new \Exception('Sabre REST Auth Failed: ' . $response->getBody());
            } catch (\Exception $e) {
                Log::error('Sabre Authentication Error', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }

        });
    }
}
