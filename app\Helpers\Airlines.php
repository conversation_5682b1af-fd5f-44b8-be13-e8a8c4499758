<?php

namespace App\Helpers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;


class Airlines
{
    public static function getAirlines()
    {
        return [
            'EK' => 'Emirates',
            'ET' => 'Ethiopian Airlines',
            'UL' => 'SriLankan Airlines',
            'QR' => 'Qatar Airways',
            'BG' => 'Biman Bangladesh Airlines',
            'TK' => 'Turkish Airlines',
            'SV' => 'Saudia',
            'EY' => 'Etihad Airways',
            'KU' => 'Kuwait Airways',
            'GF' => 'Gulf Air',
            'WY' => 'Oman Air',
            'BA' => 'British Airways',
            'AA' => 'American Airlines',
            'DL' => 'Delta Air Lines',
            'UA' => 'United Airlines',
            'AF' => 'Air France',
            'KL' => 'KLM Royal Dutch Airlines',
            'LH' => 'Lufthansa',
            'AI' => 'Air India',
            'CX' => 'Cathay Pacific',
            'SQ' => 'Singapore Airlines',
            'NH' => 'All Nippon Airways (ANA)',
            'JL' => 'Japan Airlines',
            'AC' => 'Air Canada',
            'QF' => 'Qantas',
            'NZ' => 'Air New Zealand',
            'AZ' => 'ITA Airways',
            'TK' => 'Turkish Airlines',
            'RJ' => 'Royal Jordanian',
            'MS' => 'EgyptAir',
            'IB' => 'Iberia',
            'LO' => 'LOT Polish Airlines',
            'SK' => 'Scandinavian Airlines',
            'SN' => 'Brussels Airlines',
            'KE' => 'Korean Air',
            'MH' => 'Malaysia Airlines',
            'VN' => 'Vietnam Airlines',
            'PG' => 'Bangkok Airways',
            'FD' => 'Thai AirAsia',
            'PC' => 'Pegasus Airlines',
            'PS' => 'Ukraine International Airlines',
            'LY' => 'El Al Israel Airlines',
            'AR' => 'Aerolineas Argentinas',
            'AV' => 'Avianca',
            'CM' => 'Copa Airlines',
            'LA' => 'LATAM Airlines',
            'AD' => 'Azul Brazilian Airlines',
            'G3' => 'Gol Transportes Aéreos',
            'SU' => 'Aeroflot Russian Airlines',
            'U6' => 'Ural Airlines',
            'XZ' => 'South African Airways',
            'SA' => 'South African Airways',
            'ZB' => 'Monarch Airlines (defunct)',
            'VY' => 'Vueling Airlines',
            'FR' => 'Ryanair',
            'U2' => 'easyJet',
            'W6' => 'Wizz Air',
            'XQ' => 'SunExpress',
            'FZ' => 'Flydubai',
            'SG' => 'SpiceJet',
            '6E' => 'IndiGo',
            'IX' => 'Air India Express',
            'AK' => 'AirAsia',
            'JT' => 'Lion Air',
            'PN' => 'China West Air',
            'MU' => 'China Eastern Airlines',
            'CZ' => 'China Southern Airlines',
            'CA' => 'Air China',
            'ZH' => 'Shenzhen Airlines',
            'BS'=> 'US-Bangla Airlines Ltd',

        ];
    }
}
