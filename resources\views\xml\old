<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
        <air:AirCreateReservationReq xmlns:air="http://www.travelport.com/schema/air_v52_0"
            xmlns:com="http://www.travelport.com/schema/common_v52_0" TargetBranch="{{ $targetBranch }}"
            AuthorizedBy="{{ $username }}" TraceId="{{ uniqid('bd-booking-') }}" SolutionResult="1">

            <com:BillingPointOfSaleInfo OriginApplication="UAPI"
                xmlns:com="http://www.travelport.com/schema/common_v52_0" />


                <com:HostToken Host="1G">{{ $solution['solutionKey'] }}</com:HostToken>
                <com:SSR Type="DOCS" Carrier="BS" Status="HK" FreeText="P/BD/*********/BD/01JAN80/M/01JAN30/SAYED/KHAN"/>
                {{-- <com:SSR Type="DOCS" Carrier="{{ $segment['carrier'] }}" Status="HK"
                FreeText="P/BD/{{ $passport_number }}/BD/{{ $dob }}/{{ $gender }}/{{ $expiry_date }}/{{ $last_name }}/{{ $first_name }}" /> --}}
            <!-- Passenger Information -->
            @if (is_array($passengerInfo) && count($passengerInfo))
                @foreach ($passengerInfo as $index => $passenger)
                    <com:BookingTraveler Key="PAX{{ $index + 1 }}" TravelerType="{{ $passenger['type'] }}"
                        Gender="{{ $passenger['gender'] }}">
                        <com:BookingTravelerName Prefix="{{ $passenger['prefix'] }}"
                            First="{{ $passenger['first_name'] }}" Last="{{ $passenger['last_name'] }}" />
                        <com:PhoneNumber CountryCode="880" Number="{{ $passenger['phone'] }}" Type="Mobile" />
                        @if (!empty($passenger['email']))
                            <com:Email EmailID="{{ $passenger['email'] }}" />
                        @endif
                        <com:Address>
                            <com:AddressName>Home</com:AddressName>
                            <com:Street>{{ $passenger['address']['street'] }}</com:Street>
                            <com:City>{{ $passenger['address']['city'] }}</com:City>
                            <com:State>Dhaka</com:State>
                            <com:PostalCode>1207</com:PostalCode>
                            <com:Country>BD</com:Country>
                        </com:Address>
                        @if (
                            !empty($passenger['passport']['number']) &&
                                !empty($passenger['passport']['issue_date']) &&
                                !empty($passenger['passport']['expiry_date']))
                            <com:Passport>
                                <com:Number>{{ $passenger['passport']['number'] }}</com:Number>
                                <com:IssueDate>{{ $passenger['passport']['issue_date'] }}</com:IssueDate>
                                <com:ExpirationDate>{{ $passenger['passport']['expiry_date'] }}</com:ExpirationDate>
                                <com:Nationality>BD</com:Nationality>
                            </com:Passport>
                        @endif
                    </com:BookingTraveler>
                @endforeach
            @else
                {{-- Optional: Show debug message --}}
                <p>No passenger data available.</p>
            @endif

           

            <!-- Air Pricing Solution -->
            <air:AirPricingSolution Key="{{ $solution['solutionKey'] }}">
                <air:AirItinerary>
                    <air:OriginDestinationOptions>
                        <air:OriginDestinationOption Key="{{ $solution['solutionKey'] }}">
                            @foreach ($solution['segments'] as $segment)
                                <air:FlightSegment Key="{{ $segment['key'] }}" Group="0"
                                    Carrier="{{ $segment['carrier'] }}" FlightNumber="{{ $segment['flightNumber'] }}"
                                    Origin="{{ $segment['origin'] }}" Destination="{{ $segment['destination'] }}"
                                    DepartureTime="{{ date('Y-m-d\TH:i:s', strtotime($segment['departure']['datetime'])) }}"
                                    ArrivalTime="{{ date('Y-m-d\TH:i:s', strtotime($segment['arrival']['datetime'])) }}"
                                    ETicketability="Yes" Equipment="{{ $segment['equipment'] }}" ProviderCode="1G">
                                    <air:FlightDetails Key="{{ $segment['flightDetailsRef'] }}" />
                                </air:FlightSegment>

                             

                                
                            @endforeach
                        </air:OriginDestinationOption>
                    </air:OriginDestinationOptions>
                </air:AirItinerary>

                <air:AirPricingInfo Key="{{ $solution['solutionKey'] }}"
                    BasePrice="{{ str_replace('BDT', '', $solution['basePrice']) }}"
                    Taxes="{{ $solution['taxAmount'] }}"
                    TotalPrice="{{ str_replace('BDT', '', $solution['approximateTotalPrice']) }}" PricingMethod="Sell"
                    ProviderCode="1G">

                    <air:PassengerType Code="ADT" Price="{{ str_replace('BDT', '', $solution['basePrice']) }}"
                        Taxes="{{ str_replace('BDT', '', $solution['taxAmount']) }}"
                        TotalPrice="{{ str_replace('BDT', '', $solution['approximateTotalPrice']) }}" />

                    @foreach ($solution['segments'] as $segment)
                        <air:BookingInfo BookingCode="{{ $solution['bookingClass'] }}"
                            CabinClass="{{ $solution['cabinClass'] }}" FareInfoRef="{{ $solution['solutionKey'] }}"
                            SegmentRef="{{ $segment['key'] }}" ProviderCode="1G" />
                    @endforeach

                    <!-- Baggage Allowance -->
                    @if (isset($solution['fareInfo'][0]['baggage']))
                        <air:BaggageAllowance Weight="{{ $solution['fareInfo'][0]['baggage']['value'] }}"
                            Unit="{{ $solution['fareInfo'][0]['baggage']['unit'] }}" />
                    @endif
                </air:AirPricingInfo>
            </air:AirPricingSolution>

            <!-- Action Status -->

            <air:ActionStatus Type="TAW"
                TicketDate="{{ date('Y-m-d\TH:i:s', strtotime($solution['ticketingTime'])) }}" ProviderCode="1G" />
        </air:AirCreateReservationReq>
    </s:Body>
</s:Envelope>
