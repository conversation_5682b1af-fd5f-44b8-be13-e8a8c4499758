<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Albaraka - Travel Booking</title>
    {{-- <link href="https://cdn.jsdelivr.net/npm/daisyui@4.6.0/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script> --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">




    <script src="https://unpkg.com/lucide@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>



    <!-- ✅ Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
        integrity="sha512-..." crossorigin="anonymous" referrerpolicy="no-referrer" />


</head>

<body class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-8">
                    <h1 class="text-2xl font-bold text-orange-600">Albaraka</h1>
                    <nav class="hidden md:flex space-x-6">
                        <a href="#" class="flex items-center text-gray-600 hover:text-orange-600">
                            <i data-lucide="plane" class="w-4 h-4 mr-1"></i> Flights
                        </a>
                        <a href="#" class="flex items-center text-gray-600 hover:text-orange-600">
                            <i data-lucide="hotel" class="w-4 h-4 mr-1"></i> Hotels
                        </a>
                        <a href="#" class="flex items-center text-gray-600 hover:text-orange-600">
                            <i data-lucide="car" class="w-4 h-4 mr-1"></i> Car Rental
                        </a>
                    </nav>
                </div>
                <a href="#" class="bg-green-600 text-white px-4 py-2 hover:bg-orange-700">
                    Sign In
                </a>
            </div>
        </div>
    </header>


   
    @if ($errors->any())
        @foreach ($errors->all() as $error)
            <div>{{ $error }}</div>
        @endforeach
    @endif

    



    <form id="flightSearchForm" action="{{ route('sabre.search') }}" method="get">

        <!-- Hidden Inputs to Hold Dynamic Data -->
        <input type="hidden" name="adults" id="adultsInput" value="1">
        <input type="hidden" name="children" id="childrenInput" value="0">
        <input type="hidden" name="infants" id="infantsInput" value="0">
        <input type="hidden" name="cabin_class" id="cabinClassInput" value="Economy">
        <input type="hidden" name="trip_type" id="tripTypeInput" value="oneway">

        <!-- Hidden input (will be submitted) -->
        <input type="hidden" id="origin" name="origin">
        <input type="hidden" id="destination" name="destination">




        <!-- Search Section -->
        <div class="container mx-auto px-4 py-8">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex space-x-4 mb-6">
                    <button type="button" class="px-4 py-2 rounded-md bg-orange-600 text-white"
                        onclick="setTripType('oneway')">
                        One Way
                    </button>
                    <button type="button" class="px-4 py-2 rounded-md bg-gray-100" onclick="setTripType('roundtrip')">
                        Round Trip
                    </button>

                </div>

                <div class="grid md:grid-cols-12 gap-4">
                    <div class="col-span-2 relative">
                        <label class="block text-sm font-medium text-gray-700">From</label>
                        <div class="mt-1 relative">
                            <input type="text" id="fromCity" name="fromCityDisplay" data-code="" data-airport=""
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Leaving from" autocomplete="off">
                            <i data-lucide="map-pin" class="absolute right-3 top-2.5 text-gray-400 w-5 h-5"></i>
                        </div>
                        <div id="fromSuggestions"
                            class="hidden absolute w-full bg-white mt-1 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                        </div>
                    </div>

                    <div class="col-span-2 relative">
                        <label class="block text-sm font-medium text-gray-700">To</label>
                        <div class="mt-1 relative">
                            <input type="text" id="toCity" name="Todestination"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Going to" autocomplete="off">
                            <i data-lucide="map-pin" class="absolute right-3 top-2.5 text-gray-400 w-5 h-5"></i>
                        </div>
                        <div id="toSuggestions"
                            class="hidden absolute w-full bg-white mt-1 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                        </div>
                    </div>

                    <div class="col-span-2 relative">
                        <label class="block text-sm font-medium text-gray-700">Date</label>
                        <div class="mt-1 relative">
                            <input type="text" id="datePicker" name="departure_date"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Select date">
                            <i data-lucide="calendar" class="absolute right-3 top-2.5 text-gray-400 w-5 h-5"></i>
                        </div>
                    </div>

                    <div class="col-span-2 relative" id="returnDateField" style="display: none;">
                        <label class="block text-sm font-medium text-gray-700">Return</label>
                        <div class="mt-1 relative">
                            <input type="text" id="datePicker" name="return_date"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Select date">
                            <i data-lucide="calendar" class="absolute right-3 top-2.5 text-gray-400 w-5 h-5"></i>
                        </div>
                    </div>

                    <div class="col-span-2 relative">
                        <label class="block text-sm font-medium text-gray-700">Travelers & Class</label>
                        <div class="mt-2 relative">
                            <button type="button" id="travelersBtn"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 bg-white text-left"
                                onclick="toggleTravelersModal()">
                                <span id="travelersText">1 Adult, Economy</span>
                            </button>
                            <i data-lucide="users" class="absolute right-3 top-2.5 text-gray-400 w-5 h-5"></i>
                        </div>

                        <div id="travelersModal"
                            class="hidden absolute w-full bg-white mt-1 rounded-md shadow-lg z-50 p-4">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="font-medium">Adult</h3>
                                        <p class="text-sm text-gray-500">(12+ years)</p>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" onclick="updateTravelers('adult', -1)"
                                            class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">-</button>
                                        <span id="adultCount">1</span>
                                        <button type="button" onclick="updateTravelers('adult', 1)"
                                            class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">+</button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="font-medium">Child</h3>
                                        <p class="text-sm text-gray-500">(2-12 years)</p>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" onclick="updateTravelers('child', -1)"
                                            class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">-</button>
                                        <span id="childCount">0</span>
                                        <button type="button" onclick="updateTravelers('child', 1)"
                                            class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">+</button>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="font-medium">Infant</h3>
                                        <p class="text-sm text-gray-500">(0-2 years)</p>
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" onclick="updateTravelers('infant', -1)"
                                            class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">-</button>
                                        <span id="infantCount">0</span>
                                        <button type="button" onclick="updateTravelers('infant', 1)"
                                            class="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">+</button>
                                    </div>
                                </div>

                                <div class="border-t pt-4">
                                    <h3 class="font-medium mb-2">Travel Class</h3>
                                    <div class="grid grid-cols-2 gap-2">
                                        <button type="button" onclick="setTravelClass('economy')"
                                            class="px-4 py-2 rounded bg-orange-600 text-white">Economy</button>
                                        <button type="button" onclick="setTravelClass('Premium Economy')"
                                            class="px-4 py-2 rounded border border-gray-300">Premium Economy</button>
                                        <button type="button" onclick="setTravelClass('Business')"
                                            class="px-4 py-2 rounded border border-gray-300">Business</button>
                                        <button type="button" onclick="setTravelClass('First')"
                                            class="px-4 py-2 rounded border border-gray-300">First</button>
                                    </div>
                                </div>

                                <button type="button" onclick="toggleTravelersModal()"
                                    class="w-full bg-orange-600 text-white px-4 py-2 rounded-md mt-4">
                                    Done
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-span-2 relative">
                        <button id="searchFlightsBtn" type="submit"
                            class="mt-6 w-full bg-orange-600 text-white px-6 py-3 rounded-md hover:bg-orange-700 flex items-center justify-center">
                            <i data-lucide="search" class="w-5 h-5 mr-2"></i> Search Flights
                        </button>
                    </div>


                </div>


            </div>
        </div>
    </form>

    <!-- Plane Loader -->
    <div id="flightLoader"
        class="fixed top-0 left-0 w-full h-full bg-white bg-opacity-90 z-[9999] flex items-center justify-center hidden">
        <div class="text-center">
            <img src="{{ asset('images/plan.gif') }}" alt="Loading..."
                class="w-60 h-60 rounded-full mx-auto mb-4 shadow-lg">
            <p class="text-xl text-gray-800 font-medium">Searching flights...</p>
        </div>
    </div>




    <!-- Flight Results Section -->
    <div id="flightResults" class="mt-8 container mx-auto px-4"></div>



    <section class="px-20 my-10">
        <h2 class="text-2xl font-semibold text-slate-950 mb-5">Top Searches</h2>
        <div class="grid grid-cols-3 gap-8 h-full">
            <!-- Card 1 -->
            <div
                class="w-full flex justify-evenly bg-pink-100 py-2 px-2 rounded-lg h-full hover:scale-105 hover:shadow-xl transition-all duration-300">
                <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
                    <h2 class="text-base font-medium">DAC</h2>
                    <p class="text-xs text-gray-600">04/12/2025</p>
                </div>
                <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
                    <div class="flex gap-10">
                        <img class="w-20 rounded-lg"
                            src="https://www.travomint.com/_next/image?url=%2FImage%2Fplane-dashed-route.png&w=256&q=50"
                            alt="" />
                        <h2 class="text-base font-medium">LHR</h2>
                    </div>
                    <p class="text-xs text-gray-600">A-1,C-1,B-1 Lorem adipisicing.</p>
                </div>
                <div class="ml-10">
                    <img class="w-20 h-20 rounded-lg"
                        src="https://static.vecteezy.com/system/resources/thumbnails/036/054/418/small/ai-generated-landing-a-plane-against-a-golden-sky-at-sunset-passenger-aircraft-flying-up-in-sunset-light-travelling-and-business-concept-photo.jpg"
                        alt="" />
                </div>
            </div>

            <!-- Card 2 -->
            <div
                class="w-full flex justify-evenly bg-pink-100 py-2 px-2 rounded-lg h-full hover:scale-105 hover:shadow-xl transition-all duration-300">
                <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
                    <h2 class="text-base font-medium">DAC</h2>
                    <p class="text-xs text-gray-600">04/12/2025</p>
                </div>
                <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
                    <div class="flex gap-10">
                        <img class="w-20 rounded-lg"
                            src="https://www.travomint.com/_next/image?url=%2FImage%2Fplane-dashed-route.png&w=256&q=50"
                            alt="" />
                        <h2 class="text-base font-medium">LHR</h2>
                    </div>
                    <p class="text-xs text-gray-600">A-1,C-1,B-1 Lorem adipisicing.</p>
                </div>
                <div class="ml-10">
                    <img class="w-20 h-20 rounded-lg"
                        src="https://static.vecteezy.com/system/resources/thumbnails/036/054/418/small/ai-generated-landing-a-plane-against-a-golden-sky-at-sunset-passenger-aircraft-flying-up-in-sunset-light-travelling-and-business-concept-photo.jpg"
                        alt="" />
                </div>
            </div>

            <!-- Card 3 -->
            <div
                class="w-full flex justify-evenly bg-pink-100 py-2 px-2 rounded-lg h-full hover:scale-105 hover:shadow-xl transition-all duration-300">
                <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
                    <h2 class="text-base font-medium">DAC</h2>
                    <p class="text-xs text-gray-600">04/12/2025</p>
                </div>
                <div class="grid grid-rows-2 pt-3 gap-5 flex-grow">
                    <div class="flex gap-10">
                        <img class="w-20 rounded-lg"
                            src="https://www.travomint.com/_next/image?url=%2FImage%2Fplane-dashed-route.png&w=256&q=50"
                            alt="" />
                        <h2 class="text-base font-medium">LHR</h2>
                    </div>
                    <p class="text-xs text-gray-600">A-1,C-1,B-1 Lorem adipisicing.</p>
                </div>
                <div class="ml-10">
                    <img class="w-20 h-20 rounded-lg"
                        src="https://static.vecteezy.com/system/resources/thumbnails/036/054/418/small/ai-generated-landing-a-plane-against-a-golden-sky-at-sunset-passenger-aircraft-flying-up-in-sunset-light-travelling-and-business-concept-photo.jpg"
                        alt="" />
                </div>
            </div>
        </div>
    </section>
    <!-- top airlines section -->

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">About</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-orange-400">About Us</a></li>
                        <li><a href="#" class="hover:text-orange-400">Contact</a></li>
                        <li><a href="#" class="hover:text-orange-400">Careers</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-orange-400">Help Center</a></li>
                        <li><a href="#" class="hover:text-orange-400">FAQs</a></li>
                        <li><a href="#" class="hover:text-orange-400">Privacy Policy</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Popular Routes</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-orange-400">New York - London</a></li>
                        <li><a href="#" class="hover:text-orange-400">Los Angeles - Tokyo</a></li>
                        <li><a href="#" class="hover:text-orange-400">Miami - Paris</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Newsletter</h3>
                    <p class="text-sm mb-4">Subscribe to get special offers and updates</p>
                    <div class="flex">
                        <input type="email" placeholder="Enter your email"
                            class="flex-1 px-4 py-2 rounded-l-md text-gray-900">
                        <button class="bg-orange-600 px-4 py-2 rounded-r-md hover:bg-orange-700">
                            Subscribe
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        const form = document.getElementById('flightSearchForm');
        const loader = document.getElementById('flightLoader');

        form.addEventListener('submit', function() {
            loader.classList.remove('hidden'); // show loader
        });

        // Initialize Lucide icons
        lucide.createIcons();

        // Initialize date picker with month and year dropdowns
        flatpickr("#datePicker", {
            minDate: "today",
            dateFormat: "Y-m-d",
            prevArrow: '<svg class="w-4 h-4"><path d="M15 18l-6-6 6-6" stroke="currentColor" stroke-width="2" fill="none"/></svg>',
            nextArrow: '<svg class="w-4 h-4"><path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" fill="none"/></svg>',
            locale: {
                months: {
                    shorthand: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    longhand: ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August',
                        'September', 'October', 'November', 'December'
                    ]
                }
            }
        });



        let tripType = 'oneway'; // Default trip type

        // Trip type toggle functionality
        function setTripType(type) {
            tripType = type; // Update selected trip type
            document.getElementById('tripTypeInput').value = type; // Update hidden input
            const roundBtn = document.querySelector('button:nth-child(1)');
            const oneWayBtn = document.querySelector('button:nth-child(2)');

            const returnDateField = document.getElementById('returnDateField');
            // const btnOne = document.getElementById('btnOneWay');
            // const btnRound = document.getElementById('btnRoundTrip');

            if (type === 'oneway') {
                returnDateField.style.display = 'none';
                roundBtn.classList.remove('bg-gray-100', 'text-gray-800');
                roundBtn.classList.add('bg-orange-600', 'text-white');
                oneWayBtn.classList.remove('bg-orange-600', 'text-white');
                oneWayBtn.classList.add('bg-gray-100', 'text-gray-800');
            } else {
                returnDateField.style.display = 'block';
                oneWayBtn.classList.remove('bg-gray-100', 'text-gray-800');
                oneWayBtn.classList.add('bg-orange-600', 'text-white');
                roundBtn.classList.remove('bg-orange-600', 'text-white');
                roundBtn.classList.add('bg-gray-100', 'text-gray-800');
            }


        }




        // Search validation
        document.getElementById('searchFlightsBtn').addEventListener('click', function(e) {
            const fromCity = document.getElementById('fromCity').value.trim();
            const toCity = document.getElementById('toCity').value.trim();
            const departureDate = document.getElementById('datePicker').value.trim();
            const returnDate = document.getElementById('datePicker').value.trim(); // Return date input

            // Check if 'From', 'To', and 'Departure Date' fields are filled
            if (!fromCity || !toCity || !departureDate) {
                e.preventDefault(); // Stop form submission
                Swal.fire({
                    icon: 'warning',
                    title: 'Missing fields!',
                    text: 'Please fill in From, To, and Departure Date fields.',
                    confirmButtonColor: '#f97316'
                });
                return;
            }

            // If round trip, check if return date is filled
            if (tripType === 'roundtrip' && !returnDate) {
                e.preventDefault(); // Stop form submission
                Swal.fire({
                    icon: 'warning',
                    title: 'Return date is required!',
                    text: 'Please select a return date for round trip.',
                    confirmButtonColor: '#f97316'
                });
                return;
            }
        });

        const url = './airports.json'; // or external URL if using gist

        let cities = [];

        // Fetch and prepare city data
        fetch(url)
            .then(res => res.json())
            .then(data => {
                const filtered = data.filter(item => item.code && item.code.length === 3);

                cities = filtered.map(item => ({
                    name: `${item.city}, ${item.country}`,
                    code: item.code,
                    airport: item.name
                }));

                // ✅ Enable search only AFTER cities are loaded
                setupCitySearch('fromCity', 'fromSuggestions');
                setupCitySearch('toCity', 'toSuggestions');
            })
            .catch(error => {
                console.error("Error loading JSON:", error);
            });

        // City search functionality
        function setupCitySearch(inputId, suggestionsId) {
            const input = document.getElementById(inputId);
            const suggestions = document.getElementById(suggestionsId);
            const originHiddenInput = document.getElementById('origin');
            const destinationHiddenInput = document.getElementById('destination');

            input.addEventListener('input', () => {
                const value = input.value.toLowerCase();
                const filtered = cities.filter(city =>
                    city.name.toLowerCase().includes(value) ||
                    city.code.toLowerCase().includes(value)
                );

                suggestions.innerHTML = '';
                if (value && filtered.length > 0) {
                    suggestions.classList.remove('hidden');

                    filtered.forEach(city => {
                        const div = document.createElement('div');
                        div.className = 'p-2 hover:bg-gray-100 cursor-pointer flex items-center';
                        div.innerHTML = `
          <i data-lucide="plane" class="w-4 h-4 mr-2"></i>
          <div>
            <div class="font-medium">${city.name}</div>
            <div class="text-sm text-gray-500">${city.code}, ${city.airport}</div>
          </div>
        `;
                        div.onclick = () => {
                            const selectedText = `${city.name} (${city.code})`;
                            // input.value = `${city.name} (${city.code})`;
                            if (inputId === 'fromCity') {
                                if (destinationHiddenInput.value === city.code) {
                                    Swal.fire({
                                        icon: 'warning',
                                        title: 'Same destination!',
                                        text: 'From and To destinations cannot be the same.',
                                    });
                                    return;
                                }
                                input.value = selectedText;
                                originHiddenInput.value = city.code;
                                //originHiddenInput.value = city.code;
                            } else {
                                if (originHiddenInput.value === city.code) {
                                    Swal.fire({
                                        icon: 'warning',
                                        title: 'Same destination!',
                                        text: 'From and To destinations cannot be the same.',
                                    });
                                    return;
                                }
                                input.value = selectedText;
                                destinationHiddenInput.value = city.code;
                                //destinationHiddenInput.value = city.code;
                            }
                            suggestions.innerHTML = '';
                            suggestions.classList.add('hidden');
                        };
                        suggestions.appendChild(div);
                    });

                    // Re-render Lucide icons if you're using them
                    if (window.lucide) {
                        lucide.createIcons();
                    }
                } else {
                    suggestions.classList.add('hidden');
                }
            });

            // Hide suggestions when clicking outside
            document.addEventListener('click', (e) => {
                if (!input.contains(e.target) && !suggestions.contains(e.target)) {
                    suggestions.classList.add('hidden');
                }
            });
        }



        // Travelers modal functionality
        let travelers = {
            adult: 1,
            child: 0,
            infant: 0,
            class: 'Economy'
        };

        function toggleTravelersModal() {
            const modal = document.getElementById('travelersModal');
            modal.classList.toggle('hidden');
        }

        function updateTravelers(type, change) {
            const count = travelers[type] + change;
            if (count >= 0 && count <= 9) {
                travelers[type] = count;
                document.getElementById(`${type}Count`).textContent = count;
                //document.getElementById(`${type}Input`).value = count; // Update hidden input
                if (type === 'adult') {
                    travelers.adult = count;
                } else if (type === 'child') {
                    travelers.child = count;
                } else if (type === 'infant') {
                    travelers.infant = count;
                }
                document.getElementById('adultsInput').value = travelers.adult;
                document.getElementById('childrenInput').value = travelers.child;
                document.getElementById('infantsInput').value = travelers.infant;
                // document.getElementById('cabinClassInput').value = travelers.class; // Update hidden input for class


                updateTravelersText();

            }
        }

        function setTravelClass(className) {
            travelers.class = className;
            document.getElementById('cabinClassInput').value = className; // Update hidden input for class
            // document.querySelectorAll('[onclick^="setTravelClass"]').forEach(btn => {
            //     btn.classList.remove('bg-orange-600', 'text-white');
            //     btn.classList.add('border', 'border-gray-300');
            // });

            // Set the clicked button to active state



            document.querySelectorAll('[onclick^="setTravelClass"]').forEach(btn => {
                btn.classList.remove('bg-orange-600', 'text-white');
                btn.classList.add('border', 'border-gray-300');
            });
            event.target.classList.remove('border', 'border-gray-300');
            event.target.classList.add('bg-orange-600', 'text-white');
            updateTravelersText();
        }

        function updateTravelersText() {
            const total = travelers.adult + travelers.child + travelers.infant;
            const text = `${total} Traveler${total > 1 ? 's' : ''}, ${travelers.class}`;
            document.getElementById('travelersText').textContent = text;
        }

        // Close travelers modal when clicking outside
        document.addEventListener('click', (e) => {
            const modal = document.getElementById('travelersModal');
            const btn = document.getElementById('travelersBtn');
            if (!modal.contains(e.target) && !btn.contains(e.target)) {
                modal.classList.add('hidden');
            }
        });
    </script>


</body>

</html>
