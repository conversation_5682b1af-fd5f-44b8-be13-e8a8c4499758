<?php

namespace App\Http\Controllers\Frontend;

use App\Helpers\Airlines;
use App\Http\Controllers\Controller;
use App\Services\TravelportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use SimpleXMLElement;

class FlightController extends Controller
{

    private function processFlightData($apiData)
    {
        // Example for a typical Travelport AirPriceRsp
        $flightSegments = $apiData['SOAP:Body']['air:AirPriceRsp']['air:AirSegment'] ?? [];

        $processed = [
            'airline' => $flightSegments[0]['air:Carrier'] ?? 'Unknown',
            'flight_number' => $flightSegments[0]['air:FlightNumber'] ?? '',
            'class' => $flightSegments[0]['air:BookingClass'] ?? 'Economy',
            'departure_time' => isset($flightSegments[0]['air:DepartureTime'])
                ? date('g:i A', strtotime($flightSegments[0]['air:DepartureTime']))
                : '',
            'departure_date' => isset($flightSegments[0]['air:DepartureTime'])
                ? date('M d Y', strtotime($flightSegments[0]['air:DepartureTime']))
                : '',
            // Add other fields similarly
        ];

        return $processed;
    }

    public function search(Request $request)
    {

        // return $request->all();

        $adults = $request->input('adults');
        $children = $request->input('children');
        $infants = $request->input('infants');
        $trip_type = $request->input('trip_type');


        $travelport = new TravelportService();
        $xmlResponse = $travelport->searchFlights(
            $request->input('origin'),
            $request->input('destination'),
            $request->input('departure_date'),
            $request->input('cabin_class'),
            $request->input('trip_type'),
            $request->input('return_date'),
            $adults,
            $children,
            $infants

        );

        $flights = $this->parseTravelportResponse($xmlResponse);

        //return $flights;
       // return $flights['pricingSolutions'][0]['segments'][0];

        // dd($flights);

        // $xmlResponse = response()->xml($xmlResponse);

        $pricingSolutions = $flights['pricingSolutions'];
        $segments = $flights['segments'];



        return view("frontend.flightlist", compact('pricingSolutions', 'segments'));
    }



    public function parseTravelportResponse($xmlString)
    {
        $xml = new SimpleXMLElement($xmlString);

        $xml->registerXPathNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
        $xml->registerXPathNamespace('air', 'http://www.travelport.com/schema/air_v51_0');

        $airlinename = new Airlines();
        $airline = $airlinename->getAirlines();

        // Extract all flight details
        $flightDetails = [];
        foreach ($xml->xpath('//air:FlightDetails') as $flightDetail) {
            $attrs = $flightDetail->attributes();
            $flightDetails[(string)$attrs->Key] = [
                'Key' => (string)$attrs->Key,
                'origin' => (string)$attrs->Origin,
                'destination' => (string)$attrs->Destination,
                'departureTime' => (string)$attrs->DepartureTime,
                'arrivalTime' => (string)$attrs->ArrivalTime,
                'flightTime' => (int)$attrs->FlightTime,
                'travelTime' => (int)$attrs->TravelTime,
                'equipment' => (string)$attrs->Equipment,
                'originTerminal' => isset($attrs->OriginTerminal) ? (string)$attrs->OriginTerminal : null,
                'destinationTerminal' => isset($attrs->DestinationTerminal) ? (string)$attrs->DestinationTerminal : null,
                'onTimePerformance' => isset($attrs->OnTimePerformance) ? (int)$attrs->OnTimePerformance : null,
                'distance' => isset($attrs->Distance) ? (int)$attrs->Distance : null
            ];
        }

        // Process flight segments with FlightDetails references
        $segments = [];
        foreach ($xml->xpath('//air:AirSegment') as $segment) {
            $attrs = $segment->attributes();
            $departure = new \DateTime((string)$attrs->DepartureTime);
            $arrival = new \DateTime((string)$attrs->ArrivalTime);

            // Get FlightDetails reference if exists
            $flightDetailsRef = $segment->xpath('.//air:FlightDetailsRef');
            $flightDetailsKey = $flightDetailsRef ? (string)$flightDetailsRef[0]->attributes()->Key : null;
            $flightDetailsData = $flightDetailsKey && isset($flightDetails[$flightDetailsKey]) ?
                $flightDetails[$flightDetailsKey] : null;



            $segments[(string)$attrs->Key] = [
                'carrier' => $airline[(string)$attrs->Carrier] ?? (string)$attrs->Carrier,
                'key' => (string)$attrs->Key,
                'flightNumber' => (string)$attrs->Carrier . '-' . (string)$attrs->FlightNumber,
                'origin' => (string)$attrs->Origin,
                'destination' => (string)$attrs->Destination,
                'departure' => [
                    'time' => $departure->format('h:i A'),
                    'date' => $departure->format('M d, Y'),
                    'terminal' => isset($attrs->OriginTerminal) ? (string)$attrs->OriginTerminal : ($flightDetailsData ? $flightDetailsData['originTerminal'] : 'N/A'),
                    'datetime' => (string)$attrs->DepartureTime
                ],
                'arrival' => [
                    'time' => $arrival->format('h:i A'),
                    'date' => $arrival->format('M d, Y'),
                    'terminal' => isset($attrs->DestinationTerminal) ? (string)$attrs->DestinationTerminal : ($flightDetailsData ? $flightDetailsData['destinationTerminal'] : 'N/A'),
                    'datetime' => (string)$attrs->ArrivalTime
                ],
                'duration' => $this->minutesToDuration((int)$attrs->FlightTime),
                'flightTime' => (int)$attrs->FlightTime,
                'equipment' => (string)$attrs->Equipment,
                'numberofstops' => (string)$attrs->NumberOfStops,
                //'cabinClass' => 'Economy', // Default, will be updated from pricing info
                'distance' => isset($attrs->Distance) ? (int)$attrs->Distance : ($flightDetailsData ? $flightDetailsData['distance'] : null),
                'flightDetails' => $flightDetailsData,
                'flightDetailsRef' => $flightDetailsKey,
                'availability' => [
                    'eTicketability' => (string)$attrs->ETicketability,
                    'availabilitySource' => isset($attrs->AvailabilitySource) ? (string)$attrs->AvailabilitySource : null,
                    'bookingCount' => null // Will be updated from booking info
                ]
            ];
        }

        // Process fare info
        // Initialize fareInfoMap to store fare information
        $fareInfoMap = [];
        foreach ($xml->xpath('//air:FareInfo') as $fareInfo) {
            $key = (string)$fareInfo->attributes()->Key;

            // Defensive null check
            $baggageNode = $fareInfo->xpath('.//air:BaggageAllowance/air:MaxWeight');
            $baggage = null;

            if (!empty($baggageNode)) {
                $baggageAttrs = $baggageNode[0]->attributes();
                $baggage = [
                    'value' => (string)$baggageAttrs->Value,
                    'unit' => (string)$baggageAttrs->Unit,
                ];
            }

            $fareInfoMap[$key] = [
                'fareBasis' => (string)$fareInfo->attributes()->FareBasis,
                'baggage' => $baggage
            ];
        }

        // Process pricing solutions
        $pricingSolutions = [];
        foreach ($xml->xpath('//air:AirPricingSolution') as $solution) {
            $solutionAttrs = $solution->attributes();
            $pricingInfo = $solution->xpath('.//air:AirPricingInfo')[0];
            $pricingInfoAttrs = $pricingInfo->attributes();

            // Get segment references for this solution
            $segmentRefs = $solution->xpath('.//air:AirSegmentRef');
            $FareInfoRef = $solution->xpath('.//air:FareInfoRef');
            $solutionSegments = [];

            foreach ($segmentRefs as $ref) {
                $segmentKey = (string)$ref->attributes()->Key;
                if (isset($segments[$segmentKey])) {
                    $solutionSegments[] = $segments[$segmentKey];
                }
            }

            foreach ($FareInfoRef as $ref) {
                $fareKey = (string)$ref->attributes()->Key;
                if (isset($fareInfoMap[$fareKey])) {
                    $solutionSegments[] = $fareInfoMap[$fareKey];
                }
            }


            // Get booking info to update cabin class and booking count
            foreach ($solution->xpath('.//air:BookingInfo') as $booking) {
                $bookingAttrs = $booking->attributes();
                $segmentKey = (string)$bookingAttrs->SegmentRef;

                if (isset($segments[$segmentKey])) {
                    $segments[$segmentKey]['cabinClass'] = (string)$bookingAttrs->CabinClass;
                    $segments[$segmentKey]['bookingCode'] = (string)$bookingAttrs->BookingCode;
                    $segments[$segmentKey]['bookingCount'] = (int)$bookingAttrs->BookingCount;
                }



                $solutionSegments[] = $segment;
            }

            // Process taxes
            $taxes = [];
            foreach ($solution->xpath('.//air:TaxInfo') as $tax) {
                $taxAttrs = $tax->attributes();
                $taxes[] = [
                    'category' => (string)$taxAttrs->Category,
                    'amount' => (string)$taxAttrs->Amount,
                    'currency' => substr((string)$taxAttrs->Amount, 0, 3)
                ];
            }

            // Process penalties
            $changePenalty = $solution->xpath('.//air:ChangePenalty');
            $cancelPenalty = $solution->xpath('.//air:CancelPenalty');



            $pricingSolutions[] = [
                'totalPrice' => (string)$solutionAttrs->TotalPrice,
                'basePrice' => (string)$solutionAttrs->BasePrice,
                'approximateTotalPrice' => (string)$solutionAttrs->ApproximateTotalPrice,
                'approximateBasePrice' => (string)$solutionAttrs->ApproximateBasePrice,
                'refundable' => (string)$pricingInfoAttrs->Refundable,
                'taxes' => $taxes,
                'taxAmount' => (string)$solutionAttrs->Taxes,
                'currency' => substr((string)$solutionAttrs->TotalPrice, 0, 3),
                'segments' => $solutionSegments,
                'travelTime' => $this->formatTravelTime((string)$solution->xpath('.//air:Journey')[0]->attributes()->TravelTime),
                'changePenalty' => $changePenalty ? [
                    'type' => (string)$changePenalty[0]->attributes()->PenaltyApplies,
                    'amount' => isset($changePenalty[0]->Amount) ? (string)$changePenalty[0]->Amount : null,
                    'percentage' => isset($changePenalty[0]->Percentage) ? (string)$changePenalty[0]->Percentage : null
                ] : null,
                'cancelPenalty' => $cancelPenalty ? [
                    'type' => (string)$cancelPenalty[0]->attributes()->PenaltyApplies,
                    'amount' => isset($cancelPenalty[0]->Amount) ? (string)$cancelPenalty[0]->Amount : null,
                    'percentage' => isset($cancelPenalty[0]->Percentage) ? (string)$cancelPenalty[0]->Percentage : null
                ] : null,
                'platingCarrier' => (string)$pricingInfoAttrs->PlatingCarrier,
                'airline' =>  $airline[(string)$pricingInfoAttrs->PlatingCarrier] ?? (string)$pricingInfoAttrs->PlatingCarrier,
                'providerCode' => (string)$pricingInfoAttrs->ProviderCode,
                'ticketingTime' => isset($pricingInfoAttrs->LatestTicketingTime) ?
                    (new \DateTime((string)$pricingInfoAttrs->LatestTicketingTime))->format('M d, Y h:i A') : null,
                'fareBasis' => $this->extractFareBasis($solution),
                // Add these new fields
                'bookingClass' => (string)$solution->xpath('.//air:BookingInfo')[0]->attributes()->BookingCode,
                'seatsAvailable' => (int)$solution->xpath('.//air:BookingInfo')[0]->attributes()->BookingCount,
                'cabinClass' => (string)$solution->xpath('.//air:BookingInfo')[0]->attributes()->CabinClass,
                'FareInfoRef' => $solution->xpath('.//air:FareInfoRef')[0]->attributes()->Key,
                'solutionKey' => (string)$solutionAttrs->Key
            ];
        }

        return [
            'flightDetails' => $flightDetails,
            'segments' => $segments,
            'fareInfoMap' => $fareInfoMap,
            'pricingSolutions' => $pricingSolutions
        ];
    }

    private function minutesToDuration($minutes)
    {
        $hours = floor($minutes / 60);
        $mins = $minutes % 60;
        return sprintf("%dh %02dm", $hours, $mins);
    }

    private function formatTravelTime($duration)
    {
        preg_match('/P(\d+)DT(\d+)H(\d+)M/', $duration, $matches);
        $days = $matches[1] ?? 0;
        $hours = $matches[2] ?? 0;
        $minutes = $matches[3] ?? 0;

        $result = [];
        if ($days > 0) $result[] = $days . 'd';
        if ($hours > 0) $result[] = $hours . 'h';
        if ($minutes > 0) $result[] = $minutes . 'm';

        return implode(' ', $result);
    }

    private function extractFareBasis($solution)
    {
        $fareCalc = $solution->xpath('.//air:FareCalc');
        if ($fareCalc && !empty($fareCalc[0])) {
            $fareCalcText = (string)$fareCalc[0];
            // Simple extraction of fare basis code - adjust as needed based on your FareCalc format
            if (preg_match('/\b([A-Z]{1,2}\d{1,4}[A-Z]*)\b/', $fareCalcText, $matches)) {
                return $matches[1];
            }
        }
        return null;
    }



    // In your parser
    // private function parseFlightDetailsWithPrice($xmlResponse)
    // {
    //     // ... existing code ...

    //     foreach ($airPricingSolutions as $solution) {
    //         $totalPrice = (float)$solution->xpath('.//common:TotalPrice')[0];
    //         $basePrice = (float)$solution->xpath('.//common:BasePrice')[0];
    //         $currency = (string)$solution->xpath('.//common:TotalPrice')[0]['CurrencyCode'];

    //         // Calculate discount if any
    //         $discount = 0;
    //         if ($basePrice > $totalPrice) {
    //             $discount = $basePrice - $totalPrice;
    //         }

    //         // ... rest of the parsing code ...

    //         $flights[] = [
    //             // ... existing fields ...
    //             'price' => [
    //                 'amount' => $totalPrice,
    //                 'base_amount' => $basePrice,
    //                 'currency' => $currency,
    //                 'formatted' => $currency . ' ' . number_format($totalPrice, 2),
    //                 'discount' => $discount,
    //                 'formatted_discount' => $discount > 0 ? $currency . ' ' . number_format($discount, 2) : null
    //             ],
    //             // ... rest of fields ...
    //         ];
    //     }

    //     return $flights;
    // }
}
