<style>
    .flight-search-results {
        font-family: '<PERSON><PERSON><PERSON> UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
        max-width: 900px;
        margin: 30px auto;
        padding: 0 20px;
    }

    .results-header {
        color: #2c3e50;
        text-align: center;
        margin-bottom: 30px;
        font-size: 28px;
    }

    .flight-option {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        margin-bottom: 25px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .option-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        cursor: pointer;
    }

    .option-header>div {
        flex: 1;
    }

    .price {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
        display: flex;
        align-items: center;
    }

    .price-details-toggle {
        font-size: 14px;
        color: #3498db;
        margin-left: 15px;
        font-weight: normal;
    }

    .travel-time,
    .airline {
        text-align: center;
        color: #7f8c8d;
        font-size: 15px;
    }

    .travel-time i {
        margin-right: 5px;
    }

    .option-details {
        padding: 20px;
        display: none;
    }

    .flight-option.expanded .option-details {
        display: block;
    }

    .flight-segment {
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px dashed #e0e0e0;
    }

    .flight-segment:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .segment-header {
        margin-bottom: 15px;
    }

    .segment-header .airline {
        font-weight: 600;
        color: #2c3e50;
        margin-right: 15px;
    }

    .segment-header .cabin-class {
        background-color: #e8f5e9;
        color: #388e3c;
        padding: 3px 10px;
        border-radius: 4px;
        font-size: 13px;
    }

    .segment-timings {
        display: flex;
        justify-content: space-between;
    }

    .departure,
    .arrival {
        flex: 2;
    }

    .duration {
        flex: 1;
        text-align: center;
        padding: 0 15px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .time {
        font-size: 20px;
        font-weight: 600;
        color: #2c3e50;
    }

    .date {
        color: #7f8c8d;
        margin: 5px 0;
        font-size: 14px;
    }

    .airport {
        font-size: 15px;
        color: #34495e;
    }

    .flight-duration {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .flight-distance {
        font-size: 13px;
        color: #7f8c8d;
    }

    .segment-footer {
        margin-top: 15px;
        font-size: 13px;
        color: #7f8c8d;
    }

    .pricing-details {
        margin-top: 30px;
        display: flex;
    }

    .price-breakdown {
        flex: 2;
        padding-right: 20px;
    }

    .penalty-info {
        flex: 1;
        padding-left: 20px;
        border-left: 1px solid #eee;
    }

    .breakdown-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px dashed #eee;
    }

    .breakdown-row.total {
        font-weight: 600;
        border-bottom: none;
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px solid #ddd;
    }

    .penalty {
        margin-bottom: 15px;
        font-size: 14px;
    }

    .ticketing-time {
        font-size: 14px;
        color: #e74c3c;
        margin-top: 20px;
    }

    @media (max-width: 768px) {
        .option-header {
            flex-direction: column;
            text-align: center;
        }

        .option-header>div {
            margin-bottom: 10px;
        }

        .segment-timings {
            flex-direction: column;
        }

        .departure,
        .arrival,
        .duration {
            margin-bottom: 15px;
            text-align: center;
        }

        .pricing-details {
            flex-direction: column;
        }

        .price-breakdown {
            padding-right: 0;
            margin-bottom: 20px;
        }

        .penalty-info {
            padding-left: 0;
            border-left: none;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
    }

    /* Add these new styles to your existing CSS */
    .cabin-class-badge {
        background: #4CAF50;
        color: white;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin-left: 10px;
        display: inline-flex;
        align-items: center;
    }

    .cabin-class-badge i {
        margin-right: 5px;
    }

    .seats-available {
        background: rgba(0, 0, 0, 0.1);
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin-left: 10px;
        display: inline-flex;
        align-items: center;
    }

    .seats-available i {
        margin-right: 5px;
        font-size: 0.7rem;
    }

    .baggage-info {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-top: 8px;
    }

    .baggage-text,
    .carry-on-baggage {
        background: #e3f2fd;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
    }

    .carry-on-baggage {
        background: #e8f5e9;
    }

    .baggage-text i,
    .carry-on-baggage i {
        margin-right: 5px;
    }

    .aircraft-feature {
        background: #f5f5f5;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
    }

    .aircraft-feature i {
        margin-right: 5px;
        color: #3498db;
    }
</style>
<script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle flight option details
            document.querySelectorAll('.option-header').forEach(header => {
                header.addEventListener('click', function() {
                    const option = this.closest('.flight-option');
                    const details = option.querySelector('.option-details');
    
                    // Toggle visibility
                    details.classList.toggle('hidden');
    
                    // Toggle chevron direction
                    const icon = this.querySelector('.price-details-toggle i');
                    icon.classList.toggle('fa-chevron-down');
                    icon.classList.toggle('fa-chevron-up');
                });
            });
        });
    </script>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

<div class="flight-search-results">
    <h1 class="results-header">Available Flights</h1>

    @foreach ($pricingSolutions as $solution)
        <div class="flight-option">
            <div class="option-header">
                <div class="price">
                    {{ $solution['currency'] }} {{ number_format((float) substr($solution['totalPrice'], 3), 2) }}
                    <span class="price-details-toggle">Details <i class="fas fa-chevron-down"></i></span>
                </div>
                <div class="travel-time">
                    <i class="far fa-clock"></i> {{ $solution['travelTime'] }}
                </div>
                <div class="airline">

                    {{ $solution['airline'] }} · {{ count($solution['segments']) }}
                    {{ count($solution['segments']) > 1 ? 'Segments' : 'Segment' }}
                </div>
            </div>

            <div class="option-details">

                @foreach ($solution['segments'] as $segment)
                    <div class="flight-segment">
                        <div class="segment-header">
                            <div class="airline-info">
                                {{-- <span class="airline-logo">
                                    <!-- Add airline logo here if available -->
                                    <img src="/img/airlines/{{ strtolower($segment['carrier']) }}.png"
                                        alt="{{ $segment['carrier'] }}">
                                </span> --}}
                                <span class="airline-name">{{ $segment['carrier'] }}</span>
                                <span class="flight-number">{{ $segment['flightNumber'] }}</span>
                            </div>

                            @if (isset($solution['fareInfo'][0]['baggage']))
                                <div class="baggage-info">
                                    <i class="fas fa-suitcase"></i>
                                    <span class="baggage-text">
                                        Checked:
                                        {{ $solution['fareInfo'][0]['baggage']['value'] }}{{ $solution['fareInfo'][0]['baggage']['unit'] }}
                                    </span>
                                </div>
                            @endif
                        </div>

                        <div class="segment-body">
                            <div class="departure-info">
                                <div class="time">{{ $segment['departure']['time'] }}</div>
                                <div class="date">{{ $segment['departure']['date'] }}</div>
                                <div class="airport">
                                    <span class="code">{{ $segment['origin'] }}</span>
                                    <span class="terminal">Terminal {{ $segment['departure']['terminal'] }}</span>
                                </div>
                            </div>

                            <div class="flight-info">
                                <div class="duration">
                                    <i class="fas fa-clock"></i> {{ $segment['duration'] }}
                                </div>
                                <div class="stops">
                                    @if ($segment['numberofstops'] > 0)
                                        {{ $segment['numberofstops'] }} stop(s)
                                    @else
                                        Non-stop
                                    @endif
                                </div>
                                <div class="aircraft">
                                    <i class="fas fa-plane"></i> {{ $segment['equipment'] }}
                                </div>
                            </div>

                            <div class="arrival-info">
                                <div class="time">{{ $segment['arrival']['time'] }}</div>
                                <div class="date">{{ $segment['arrival']['date'] }}</div>
                                <div class="airport">
                                    <span class="code">{{ $segment['destination'] }}</span>
                                    <span class="terminal">Terminal {{ $segment['arrival']['terminal'] }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="segment-footer">
                            @if ($segment['equipment'] === '388')
                                <!-- Airbus A380 -->
                                <div class="aircraft-feature">
                                    <i class="fas fa-wifi"></i> WiFi Available
                                </div>
                            @endif

                            <div class="cabin-class">
                                <i class="fas fa-chair"></i> {{ $solution['cabinClass'] }}
                            </div>
                        </div>
                    </div>
                @endforeach

                <!-- Display the general baggage allowance -->
                @if (isset($solution['fareInfo'][0]['baggage']))
                    <div class="baggage-summary">
                        <h4>Baggage Allowance</h4>
                        <ul>
                            <li>
                                <i class="fas fa-suitcase"></i> Checked baggage:
                                {{ $solution['fareInfo'][0]['baggage']['value'] }}{{ $solution['fareInfo'][0]['baggage']['unit'] }}
                                per passenger
                            </li>
                            <li>
                                <i class="fas fa-briefcase"></i> Carry-on: 1 piece (7kg max)
                            </li>
                        </ul>
                    </div>
                @endif

                <div class="segment-header">

                    <span class="cabin-class">{{ $solution['cabinClass'] }}</span>
                    <span class="cabin-class">{{ $solution['seatsAvailable'] }}</span>
                    <span class="cabin-class">{{ $solution['bookingClass'] }}</span>
                </div>



                <div class="pricing-details">
                    <div class="price-breakdown">
                        <div class="breakdown-row">
                            <span>Base Fare:</span>
                            <span>{{ $solution['currency'] }}
                                {{ number_format((float) substr($solution['basePrice'], 3), 2) }}</span>
                        </div>

                        @foreach ($solution['taxes'] as $tax)
                            <div class="breakdown-row">
                                <span>Tax ({{ $tax['category'] }}):</span>
                                <span>{{ $solution['currency'] }}
                                    {{ number_format((float) substr($tax['amount'], 3), 2) }}</span>

                            </div>
                        @endforeach

                        <div class="breakdown-row total">
                            <span>Total Price:</span>
                            <span>{{ $solution['totalPrice'] }}</span>
                        </div>
                    </div>

                    <div class="penalty-info">
                        <div class="penalty">
                            <strong>Change Penalty:</strong>
                            @if ($solution['changePenalty']['percentage'])
                                {{ $solution['changePenalty']['percentage'] }}% of fare
                            @elseif($solution['changePenalty']['amount'])
                                {{ $solution['currency'] }} {{ $solution['changePenalty']['amount'] }}
                            @else
                                {{ $solution['changePenalty']['type'] }}
                            @endif
                        </div>

                        <div class="penalty">
                            <strong>Cancel Penalty:</strong>
                            @if ($solution['cancelPenalty']['percentage'])
                                {{ $solution['cancelPenalty']['percentage'] }}% of fare
                            @elseif($solution['cancelPenalty']['amount'])
                                {{ $solution['currency'] }} {{ $solution['cancelPenalty']['amount'] }}
                            @else
                                {{ $solution['cancelPenalty']['type'] }}
                            @endif
                        </div>

                        @if ($solution['ticketingTime'])
                            <div class="ticketing-time">
                                <strong>Ticket by:</strong> {{ $solution['ticketingTime'] }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endforeach
</div>
