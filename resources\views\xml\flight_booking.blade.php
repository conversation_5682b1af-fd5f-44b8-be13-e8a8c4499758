<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/">
    <s:Body xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">



        <air:AirCreateReservationReq xmlns:air="http://www.travelport.com/schema/air_v51_0"
            xmlns:com="http://www.travelport.com/schema/common_v51_0" TargetBranch="{{ $targetBranch }}"
            AuthorizedBy="{{ $username }}" TraceId="b2b-booking" SolutionResult="1">
            <com:BillingPointOfSaleInfo OriginApplication="UAPI" />

            <!-- Passenger Info -->
            <com:BookingTraveler Key="PAX1" TravelerType="ADT" Gender="M">
                <com:BookingTravelerName Prefix="Mr" First="John" Last="Doe" />
                <com:PhoneNumber Number="8801999999999" Type="Mobile" />
                <com:Email EmailID="<EMAIL>" />
                <com:Address>
                    <com:AddressName>Home</com:AddressName>
                    <com:Street>Road 123</com:Street>
                    <com:City>Dhaka</com:City>
                    <com:Country>BD</com:Country>
                </com:Address>
            </com:BookingTraveler>

            <!-- Air Pricing Solution (from Search) -->
            <air:AirPricingSolution Key="NIXiJZSqWDKAkCJzCAAAAA==">
                <air:AirItinerary>
                    <air:OriginDestinationOptions>
                        <air:OriginDestinationOption Key="NIXiJZSqWDKAkCJzCAAAAA==">
                            <air:FlightSegment Key="NIXiJZSqWDKAkCJzCAAAAA==" Group="0" Carrier="BG"
                                FlightNumber="1234" Origin="DAC" Destination="DAC" DepartureTime="2023-10-01T10:00:00"
                                ArrivalTime="2023-10-01T12:00:00" ETicketability="Yes" Equipment="320"
                                PolledFlight="false" ProviderCode="1G">
                                <air:FlightDetails Key="NIXiJZSqWDKAkCJzCAAAAA==" />
                            </air:FlightSegment>
                        </air:OriginDestinationOption>
                    </air:OriginDestinationOptions>
                </air:AirItinerary>
                <air:AirPricingInfo Key="NIXiJZSqWDKAkCJzCAAAAA==" BasePrice="100.00" EquivalentBasePrice="100.00"
                    Taxes="20.00" EquivalentTaxes="20.00" TotalPrice="120.00" EquivalentTotalPrice="120.00"
                    PricingMethod="Sell" ProviderCode="1G" IsPriceQuote="false">
                    <air:PassengerType Code="ADT" Price="100.00" Taxes="20.00" TotalPrice="120.00" />

                    <air:BookingInfo BookingCode="Y" CabinClass="Y" FareInfoRef="NIXiJZSqWDKAkCJzCAAAAA=="
                        SegmentRef="NIXiJZSqWDKAkCJzCAAAAA==" ProviderCode="1G" />
                    <air:ActionStatus Type="TAW" TicketDate="2025-04-21T20:24:00" ProviderCode="1G" />

            </air:AirPricingSolution>

            

                    <!-- Passenger Information -->
                    @if (is_array($passengerInfo) && count($passengerInfo))
                        @foreach ($passengerInfo as $index => $passenger)
                            <com:BookingTraveler Key="PAX{{ $index + 1 }}" TravelerType="{{ $passenger['type'] }}"
                                Gender="{{ $passenger['gender'] }}">
                                <com:BookingTravelerName Prefix="{{ $passenger['prefix'] }}"
                                    First="{{ strtoupper($passenger['first_name']) }}"
                                    Last="{{ strtoupper($passenger['last_name']) }}" />
                                <com:PhoneNumber CountryCode="880" Number="{{ $passenger['phone'] }}"
                                    Type="Mobile" />
                                @if (!empty($passenger['email']))
                                    <com:Email EmailID="{{ $passenger['email'] }}" />
                                @endif
                                <com:Address>
                                    <com:AddressName>Home</com:AddressName>
                                    <com:Street>{{ $passenger['address']['street'] }}</com:Street>
                                    <com:City>{{ $passenger['address']['city'] }}</com:City>
                                    <com:State>Dhaka</com:State>
                                    <com:PostalCode>1207</com:PostalCode>
                                    <com:Country>BD</com:Country>
                                </com:Address>

                                <com:Passport>
                                    <com:Number>A01813902</com:Number>
                                    <com:IssueDate>2022-09-30</com:IssueDate>
                                    <com:ExpirationDate>2032-08-31</com:ExpirationDate>
                                    <com:Nationality>BD</com:Nationality>
                                </com:Passport>


                                <com:SSR Type="DOCS" Carrier="BS" Status="HK"
                                    FreeText="P/BD/A01813902/BD/01JAN80/M/31AUG32/KHAN/SAYED" />

                            </com:BookingTraveler>
                        @endforeach
                    @else
                        {{-- Optional: Show debug message --}}
                        <p>No passenger data available.</p>
                    @endif


                    <com:HostToken Host="1G">{{ $solution['solutionKey'] }}</com:HostToken>
                    <!-- Air Pricing Solution -->
                    <air:AirPricingSolution Key="{{ $solution['solutionKey'] }}">
                        <air:AirItinerary>
                            <air:OriginDestinationOptions>
                                <air:OriginDestinationOption Key="{{ $solution['solutionKey'] }}">
                                    @foreach ($solution['segments'] as $segment)
                                        <air:FlightSegment Key="{{ $segment['key'] }}" Group="0"
                                            Carrier="{{ $segment['carrier'] }}" FlightNumber="101"
                                            Origin="{{ $segment['origin'] }}"
                                            Destination="{{ $segment['destination'] }}"
                                            DepartureTime="{{ date('Y-m-d\TH:i:s', strtotime($segment['departure']['datetime'])) }}"
                                            ArrivalTime="{{ date('Y-m-d\TH:i:s', strtotime($segment['arrival']['datetime'])) }}"
                                            ETicketability="Yes" Equipment="{{ $segment['equipment'] }}"
                                            ProviderCode="1G">
                                            <air:FlightDetails Key="{{ $segment['flightDetailsRef'] }}" />
                                        </air:FlightSegment>
                                    @endforeach
                                </air:OriginDestinationOption>
                            </air:OriginDestinationOptions>
                        </air:AirItinerary>



                        <air:AirPricingInfo Key="{{ $solution['solutionKey'] }}"
                            BasePrice="{{ str_replace('BDT', '', $solution['basePrice']) }}"
                            Taxes="{{ $solution['taxAmount'] }}"
                            TotalPrice="{{ str_replace('BDT', '', $solution['approximateTotalPrice']) }}"
                            PricingMethod="Sell" ProviderCode="1G">

                            <air:PassengerType Code="ADT" Price="7401" Taxes="2596" TotalPrice="9997" />

                            @foreach ($solution['segments'] as $segment)
                                <air:BookingInfo BookingCode="{{ $solution['bookingClass'] }}"
                                    CabinClass="{{ $solution['cabinClass'] }}"
                                    FareInfoRef="{{ $solution['solutionKey'] }}" SegmentRef="{{ $segment['key'] }}"
                                    ProviderCode="1G" />
                            @endforeach

                            <!-- Baggage Allowance -->
                            @if (isset($solution['fareInfo'][0]['baggage']))
                                <air:BaggageAllowance Weight="{{ $solution['fareInfo'][0]['baggage']['value'] }}"
                                    Unit="{{ $solution['fareInfo'][0]['baggage']['unit'] }}" />
                            @endif
                        </air:AirPricingInfo>
                    </air:AirPricingSolution>




            <air:ActionStatus Type="TAW" TicketDate="2025-04-21T20:24:00" ProviderCode="1G" />
        </air:AirCreateReservationReq>
    </s:Body>
</s:Envelope>
