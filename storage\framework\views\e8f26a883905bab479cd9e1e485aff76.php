<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flight Search Results</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-4">Flight Search Results</h1>
            <div class="flex justify-between items-center mb-4">
                <p class="text-gray-600">Found <span class="font-semibold"><?php echo e(isset($formattedFlights) ? count($formattedFlights) : 0); ?></span> itineraries</p>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition">
                        <i class="fas fa-filter mr-2"></i>Filter
                    </button>
                    <button class="px-4 py-2 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition">
                        <i class="fas fa-sort mr-2"></i>Sort
                    </button>
                </div>
            </div>

            <div class="max-w-4xl mx-auto p-4">
                <h2 class="text-xl font-bold mb-4">Available Flights</h2>

                <?php if(isset($formattedFlights) && count($formattedFlights) > 0): ?>
                    <?php $__currentLoopData = $formattedFlights; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $flight): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php echo $__env->make('components.flight-card', $flight, array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <div class="text-gray-500 text-lg mb-2">
                            <i class="fas fa-plane-slash text-4xl mb-4"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">No flights found</h3>
                        <p class="text-gray-500">Please try adjusting your search criteria.</p>
                    </div>
                <?php endif; ?>
            </div>


        </div>
    </div>
</body>

</html>
<?php /**PATH E:\xampp\htdocs\albaraka\resources\views/flight_list.blade.php ENDPATH**/ ?>