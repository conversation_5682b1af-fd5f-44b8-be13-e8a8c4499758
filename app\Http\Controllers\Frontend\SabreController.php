<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Services\SabreAuthService;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class SabreController extends Controller
{

    public function getToken(Request $request)
    {

        $client = new Client([
            'base_uri' => 'https://api.cert.platform.sabre.com',
            'headers' => [
                'Authorization' => 'Basic ' . env('SABRE_SECRET'),
                'Content-Type' => 'application/x-www-form-urlencoded'
            ]
        ]);

        try {
            $response = $client->post('/v2/auth/token', [
                'form_params' => [
                    'grant_type' => 'client_credentials'
                ]
            ]);

            if ($response->getStatusCode() == 200) {
                $responseBody = json_decode($response->getBody(), true);
                return $responseBody['access_token'];
            }

            throw new \Exception('Sabre REST Auth Failed: ' . $response->getBody());
        } catch (\Exception $e) {
            Log::error('Sabre Authentication Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }


    public function searchFlights(Request $request)
    {

        // return $request->all();
        $validated = $request->validate([
            'origin' => 'required|string|size:3',
            'destination' => 'required|string|size:3',
            'departure_date' => 'required|date|after_or_equal:today',
            'adults' => 'required|integer|min:1',
            'children' => 'nullable|integer|min:0',
            'infants' => 'nullable|integer|min:0',
            'trip_type' => 'required|in:oneway,roundtrip',
            'return_date' => [
                'nullable',
                'date',
                Rule::requiredIf(function () use ($request) {
                    return $request->input('trip_type') === 'roundtrip';
                }),
                'after_or_equal:departure_date'
            ],
        ]);


        // Custom validation for return_date when trip_type is roundtrip
        if ($validated['trip_type'] === 'roundtrip' && empty($validated['return_date'])) {
            return response()->json([
                'error' => 'Validation failed',
                'message' => 'Return date is required for roundtrip flights'
            ], 422);
        }

        try {
            // Get access token
            $accessToken = app(SabreAuthService::class)->getAccessToken();

            $origin = $request->input('origin');
            $destination = $request->input('destination');
            $departureDate = $request->input('departure_date');
            $returnDate = $request->input('return_date');

            $adults = $request->input('adults');
            $children = $request->input('children');
            $infants = $request->input('infants');
            $trip_type = $request->input('trip_type');

            $passengercount = $adults + $children + $infants;

            // Build PassengerTypeQuantity array
            $passengerTypeQuantity = [];
            if ($adults > 0) {
                $passengerTypeQuantity[] = [
                    "Quantity" => $adults,
                    "Code" => "ADT",
                    "TPA_Extensions" => [
                        "VoluntaryChanges" => ["Match" => "Info"]
                    ]
                ];
            }
            if ($children > 0) {
                $passengerTypeQuantity[] = [
                    "Quantity" => $children,
                    "Code" => "C03",
                    "TPA_Extensions" => [
                        "VoluntaryChanges" => ["Match" => "Info"]
                    ]
                ];
            }
            if ($infants > 0) {
                $passengerTypeQuantity[] = [
                    "Quantity" => $infants,
                    "Code" => "INF",
                    "TPA_Extensions" => [
                        "VoluntaryChanges" => ["Match" => "Info"]
                    ]
                ];
            }

            // Build request payload
            $payload = [
                "OTA_AirLowFareSearchRQ" => [
                    "Version" => "5.0.0",
                    "POS" => [
                        "Source" => [
                            [
                                "PseudoCityCode" => env('SABRE_PCC', '3IZK'),
                                "RequestorID" => [
                                    "Type" => "1",
                                    "ID" => "1",
                                    "CompanyName" => [
                                        "Code" => "TN"
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "OriginDestinationInformation" => [
                        [
                            "RPH" => "1",
                            "DepartureDateTime" => Carbon::parse($validated['departure_date'])->format('Y-m-d\TH:i:s'),
                            "OriginLocation" => [
                                "LocationCode" => strtoupper($validated['origin'])
                            ],
                            "DestinationLocation" => [
                                "LocationCode" => strtoupper($validated['destination'])
                            ]
                        ]
                    ],
                    "TravelPreferences" => [
                        "MaxStopsQuantity" => 4,
                        "TPA_Extensions" => [
                            "LongConnectTime" => [
                                "Enable" => true,
                                "Max" => 1439,
                                "Min" => 180
                            ],
                            "XOFares" => ["Value" => true],
                            "FlexibleFares" => [
                                "FareParameters" => [
                                    [
                                        "RefundPenalty" => ["Ind" => true],
                                        "VoluntaryChanges" => ["Match" => "Info"]
                                    ]
                                ]
                            ]
                        ],
                        "Baggage" => [
                            "RequestType" => "C",
                            "Description" => true
                        ]
                    ],
                    "TravelerInfoSummary" => [
                        "SeatsRequested" => [$validated['adults'] + ($validated['children'] ?? 0) + ($validated['infants'] ?? 0)],
                        "AirTravelerAvail" => [
                            [
                                "PassengerTypeQuantity" => $this->buildPassengerTypes($validated)
                            ]
                        ]
                    ],
                    "TPA_Extensions" => [
                        "IntelliSellTransaction" => [
                            "RequestType" => ["Name" => "50ITINS"]
                        ]
                    ]
                ]
            ];

            // Add return segment for roundtrip
            if ($validated['trip_type'] === 'roundtrip') {
                $payload['OTA_AirLowFareSearchRQ']['OriginDestinationInformation'][] = [
                    "RPH" => "2",
                    "DepartureDateTime" => Carbon::parse($validated['return_date'])->format('Y-m-d\TH:i:s'),
                    "OriginLocation" => [
                        "LocationCode" => strtoupper($validated['destination'])
                    ],
                    "DestinationLocation" => [
                        "LocationCode" => strtoupper($validated['origin'])
                    ]
                ];
            }


           // return $payload;


            // Make API request
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json'
            ])->post('https://api.cert.platform.sabre.com/v5/offers/shop', $payload);

            if (!$response->successful()) {
                throw new \Exception('Sabre API Error: ' . $response->body());
            }

            $flights = $response->json();

            return view("flight_list", compact('flights'));

            //return response()->json($response->json());

        } catch (\Exception $e) {
            Log::error('Flight Search Error', [
                'error' => $e->getMessage(),
                'payload' => $payload ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to search flights',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    protected function buildPassengerTypes(array $data): array
    {
        $passengers = [];

        // Adult passengers
        if (!empty($data['adults'])) {
            $passengers[] = [
                "Code" => "ADT",
                "Quantity" => (int) $data['adults'],
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }

        // Child passengers (age 2-11)
        if (!empty($data['children'])) {
            $passengers[] = [
                "Code" => "CNN",
                "Quantity" => (int) $data['children'],
                "Age" => 8,
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }

        // Infant passengers (under 2)
        if (!empty($data['infants'])) {
            $passengers[] = [
                "Code" => "INF",
                "Quantity" => (int) $data['infants'],
                "Age" => 1,
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }

        return $passengers;
    }



    public function searchFlights_old(Request $request)
    {

        $request->validate([
            'origin' => 'required|string|size:3',
            'destination' => 'required|string|size:3',
            'departure_date' => 'required|date',
            'adults' => 'required|integer|min:1',
            'children' => 'nullable|integer|min:0',
            'infants' => 'nullable|integer|min:0',
            'trip_type' => 'required|in:one,round',
            //'return_date' => 'required_if:trip_type,round|date|after_or_equal:departure_date',
        ]);

        $origin = $request->input('origin');
        $destination = $request->input('destination');
        $departureDate = $request->input('departure_date');
        $returnDate = $request->input('return_date');

        $adults = $request->input('adults');
        $children = $request->input('children');
        $infants = $request->input('infants');
        $trip_type = $request->input('trip_type');

        $passengercount = $adults + $children + $infants;

        // Build PassengerTypeQuantity array
        $passengerTypeQuantity = [];
        if ($adults > 0) {
            $passengerTypeQuantity[] = [
                "Quantity" => $adults,
                "Code" => "ADT",
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }
        if ($children > 0) {
            $passengerTypeQuantity[] = [
                "Quantity" => $children,
                "Code" => "C03",
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }
        if ($infants > 0) {
            $passengerTypeQuantity[] = [
                "Quantity" => $infants,
                "Code" => "INF",
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }


        $saber_url = 'https://api.cert.platform.sabre.com/v5/offers/shop';

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('SABRE_Token'),
            'Content-Type' => 'application/json'
        ])->post($saber_url, [
            "OTA_AirLowFareSearchRQ" => [
                "Version" => "5",
                "POS" => [
                    "Source" => [
                        [
                            "PseudoCityCode" => env('SABRE_PCC'),
                            "RequestorID" => [
                                "Type" => "1",
                                "ID" => "1",
                                "CompanyName" => [
                                    "Code" => "TN",
                                    "content" => "TN"
                                ]
                            ]
                        ]
                    ]
                ],

                "AvailableFlightsOnly" => true,
                "OriginDestinationInformation" => array_values(array_filter([
                    [
                        "RPH" => "1",
                        "DepartureDateTime" => $departureDate,
                        "OriginLocation" => ["LocationCode" => $origin],
                        "DestinationLocation" => ["LocationCode" => $destination],
                        "TPA_Extensions" => (object)[] // empty for now
                    ],
                    $returnDate ? [
                        "RPH" => "2",
                        "DepartureDateTime" => $returnDate,
                        "OriginLocation" => ["LocationCode" => $destination],
                        "DestinationLocation" => ["LocationCode" => $origin],
                        "TPA_Extensions" => (object)[]
                    ] : null
                ])),

                "TravelPreferences" => [
                    "MaxStopsQuantity" => 4,
                    "TPA_Extensions" => [
                        "LongConnectTime" => [
                            "Enable" => true,
                            "Max" => 1439,
                            "Min" => 180
                        ],
                        "XOFares" => ["Value" => true],
                        "FlexibleFares" => [
                            "FareParameters" => [
                                [
                                    "RefundPenalty" => ["Ind" => true],
                                    "VoluntaryChanges" => ["Match" => "Info"]
                                ]
                            ]
                        ]
                    ],
                    "Baggage" => [
                        "RequestType" => "C",
                        "Description" => true
                    ]
                ],

                "TravelerInfoSummary" => [
                    "SeatsRequested" => [$passengercount],
                    "AirTravelerAvail" => [
                        [
                            "PassengerTypeQuantity" => $passengerTypeQuantity
                        ]
                    ]
                ],

                "TPA_Extensions" => [
                    "IntelliSellTransaction" => [
                        "RequestType" =>  ["Name" => "50ITINS"],
                    ]

                ]

            ]

        ]);



        return $response->json();
    }
}
