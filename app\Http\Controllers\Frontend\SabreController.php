<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Services\SabreAuthService;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class SabreController extends Controller
{

    public function getToken(Request $request)
    {

        $client = new Client([
            'base_uri' => 'https://api.cert.platform.sabre.com',
            'headers' => [
                'Authorization' => 'Basic ' . env('SABRE_SECRET'),
                'Content-Type' => 'application/x-www-form-urlencoded'
            ]
        ]);

        try {
            $response = $client->post('/v2/auth/token', [
                'form_params' => [
                    'grant_type' => 'client_credentials'
                ]
            ]);

            if ($response->getStatusCode() == 200) {
                $responseBody = json_decode($response->getBody(), true);
                return $responseBody['access_token'];
            }

            throw new \Exception('Sabre REST Auth Failed: ' . $response->getBody());
        } catch (\Exception $e) {
            Log::error('Sabre Authentication Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }


    public function searchFlights(Request $request)
    {

        // return $request->all();
        $validated = $request->validate([
            'origin' => 'required|string|size:3',
            'destination' => 'required|string|size:3',
            'departure_date' => 'required|date|after_or_equal:today',
            'adults' => 'required|integer|min:1',
            'children' => 'nullable|integer|min:0',
            'infants' => 'nullable|integer|min:0',
            'trip_type' => 'required|in:oneway,roundtrip',
            'return_date' => [
                'nullable',
                'date',
                Rule::requiredIf(function () use ($request) {
                    return $request->input('trip_type') === 'roundtrip';
                }),
                'after_or_equal:departure_date'
            ],
        ]);


        // Custom validation for return_date when trip_type is roundtrip
        if ($validated['trip_type'] === 'roundtrip' && empty($validated['return_date'])) {
            return response()->json([
                'error' => 'Validation failed',
                'message' => 'Return date is required for roundtrip flights'
            ], 422);
        }

        try {
            // Get access token
            $accessToken = app(SabreAuthService::class)->getAccessToken();

            $origin = $request->input('origin');
            $destination = $request->input('destination');
            $departureDate = $request->input('departure_date');
            $returnDate = $request->input('return_date');

            $adults = $request->input('adults');
            $children = $request->input('children');
            $infants = $request->input('infants');
            $trip_type = $request->input('trip_type');

            $passengercount = $adults + $children + $infants;

            // Build PassengerTypeQuantity array
            $passengerTypeQuantity = [];
            if ($adults > 0) {
                $passengerTypeQuantity[] = [
                    "Quantity" => $adults,
                    "Code" => "ADT",
                    "TPA_Extensions" => [
                        "VoluntaryChanges" => ["Match" => "Info"]
                    ]
                ];
            }
            if ($children > 0) {
                $passengerTypeQuantity[] = [
                    "Quantity" => $children,
                    "Code" => "C03",
                    "TPA_Extensions" => [
                        "VoluntaryChanges" => ["Match" => "Info"]
                    ]
                ];
            }
            if ($infants > 0) {
                $passengerTypeQuantity[] = [
                    "Quantity" => $infants,
                    "Code" => "INF",
                    "TPA_Extensions" => [
                        "VoluntaryChanges" => ["Match" => "Info"]
                    ]
                ];
            }

            // Build request payload
            $payload = [
                "OTA_AirLowFareSearchRQ" => [
                    "Version" => "5.0.0",
                    "POS" => [
                        "Source" => [
                            [
                                "PseudoCityCode" => env('SABRE_PCC', '3IZK'),
                                "RequestorID" => [
                                    "Type" => "1",
                                    "ID" => "1",
                                    "CompanyName" => [
                                        "Code" => "TN"
                                    ]
                                ]
                            ]
                        ]
                    ],
                    "OriginDestinationInformation" => [
                        [
                            "RPH" => "1",
                            "DepartureDateTime" => Carbon::parse($validated['departure_date'])->format('Y-m-d\TH:i:s'),
                            "OriginLocation" => [
                                "LocationCode" => strtoupper($validated['origin'])
                            ],
                            "DestinationLocation" => [
                                "LocationCode" => strtoupper($validated['destination'])
                            ]
                        ]
                    ],
                    "TravelPreferences" => [
                        "MaxStopsQuantity" => 4,
                        "TPA_Extensions" => [
                            "LongConnectTime" => [
                                "Enable" => true,
                                "Max" => 1439,
                                "Min" => 180
                            ],
                            "XOFares" => ["Value" => true],
                            "FlexibleFares" => [
                                "FareParameters" => [
                                    [
                                        "RefundPenalty" => ["Ind" => true],
                                        "VoluntaryChanges" => ["Match" => "Info"]
                                    ]
                                ]
                            ]
                        ],
                        "Baggage" => [
                            "RequestType" => "C",
                            "Description" => true
                        ]
                    ],
                    "TravelerInfoSummary" => [
                        "SeatsRequested" => [$validated['adults'] + ($validated['children'] ?? 0) + ($validated['infants'] ?? 0)],
                        "AirTravelerAvail" => [
                            [
                                "PassengerTypeQuantity" => $this->buildPassengerTypes($validated)
                            ]
                        ]
                    ],
                    "TPA_Extensions" => [
                        "IntelliSellTransaction" => [
                            "RequestType" => ["Name" => "50ITINS"]
                        ]
                    ]
                ]
            ];

            // Add return segment for roundtrip
            if ($validated['trip_type'] === 'roundtrip') {
                $payload['OTA_AirLowFareSearchRQ']['OriginDestinationInformation'][] = [
                    "RPH" => "2",
                    "DepartureDateTime" => Carbon::parse($validated['return_date'])->format('Y-m-d\TH:i:s'),
                    "OriginLocation" => [
                        "LocationCode" => strtoupper($validated['destination'])
                    ],
                    "DestinationLocation" => [
                        "LocationCode" => strtoupper($validated['origin'])
                    ]
                ];
            }


            // return $payload;


            // Make API request
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json'
            ])->post('https://api.cert.platform.sabre.com/v5/offers/shop', $payload);

            if (!$response->successful()) {
                throw new \Exception('Sabre API Error: ' . $response->body());
            }

            $flights = $response->json();

            // Parse and format flight data
            $formattedFlights = $this->parseFlightResponse($flights);

            return view("flight_list", compact('formattedFlights', 'flights'));

            //return response()->json($response->json());

        } catch (\Exception $e) {
            Log::error('Flight Search Error', [
                'error' => $e->getMessage(),
                'payload' => $payload ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to search flights',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    protected function buildPassengerTypes(array $data): array
    {
        $passengers = [];

        // Adult passengers
        if (!empty($data['adults'])) {
            $passengers[] = [
                "Code" => "ADT",
                "Quantity" => (int) $data['adults'],
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }

        // Child passengers (age 2-11)
        if (!empty($data['children'])) {
            $passengers[] = [
                "Code" => "CNN",
                "Quantity" => (int) $data['children'],
                "Age" => 8,
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }

        // Infant passengers (under 2)
        if (!empty($data['infants'])) {
            $passengers[] = [
                "Code" => "INF",
                "Quantity" => (int) $data['infants'],
                "Age" => 1,
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }

        return $passengers;
    }

    /**
     * Parse Sabre flight response and format for display
     */
    protected function parseFlightResponse(array $response): array
    {
        $formattedFlights = [];

        if (!isset($response['groupedItineraryResponse']['itineraryGroups'])) {
            return $formattedFlights;
        }

        foreach ($response['groupedItineraryResponse']['itineraryGroups'] as $group) {
            if (!isset($group['itineraries'])) {
                continue;
            }

            foreach ($group['itineraries'] as $itinerary) {
                $formattedFlight = $this->formatItinerary($itinerary, $response);
                if ($formattedFlight) {
                    $formattedFlights[] = $formattedFlight;
                }
            }
        }

        return $formattedFlights;
    }

    /**
     * Format individual itinerary
     */
    protected function formatItinerary(array $itinerary, array $fullResponse): ?array
    {
        if (!isset($itinerary['legs']) || empty($itinerary['legs'])) {
            return null;
        }

        $leg = $itinerary['legs'][0]; // First leg for display
        $scheduleDescs = $fullResponse['groupedItineraryResponse']['scheduleDescs'] ?? [];

        // Find schedule description for this leg
        $scheduleDesc = null;
        foreach ($scheduleDescs as $desc) {
            if ($desc['id'] == $leg['ref']) {
                $scheduleDesc = $desc;
                break;
            }
        }

        if (!$scheduleDesc) {
            return null;
        }

        // Get pricing information
        $pricingInfo = $itinerary['pricingInformation'][0] ?? null;
        if (!$pricingInfo) {
            return null;
        }


        return [
            'id' => $itinerary['id'] ?? uniqid(),
            'airlineName' => $this->getAirlineName($scheduleDesc['carrier']['marketing']),
            'airlineCode' => $scheduleDesc['carrier']['marketing'],
            'flightNumber' => $scheduleDesc['carrier']['marketing'] . ' ' . $scheduleDesc['carrier']['marketingFlightNumber'],
            'logoUrl' => $this->getAirlineLogo($scheduleDesc['carrier']['marketing']),
            'departureAirport' => $scheduleDesc['departure']['airport'],
            'arrivalAirport' => $scheduleDesc['arrival']['airport'],
            'departureTime' => $this->formatTime($scheduleDesc['departure']['time']),
            'arrivalTime' => $this->formatTime($scheduleDesc['arrival']['time']),
            'departureDate' => $this->formatDate($scheduleDesc['departure']['time']),
            'arrivalDate' => $this->formatDate($scheduleDesc['arrival']['time']),
            'duration' => $this->formatDuration($scheduleDesc['elapsedTime']),
            'stops' => $scheduleDesc['stopCount'],
            'price' =>$pricingInfo['fare']['totalFare']['totalPrice'],
            'originalPrice' => $pricingInfo['fare']['totalFare']['totalPrice'],
            'currency' => $pricingInfo['fare']['totalFare']['currency'],
            'eTicketable' => $scheduleDesc['eTicketable'],
            'cabinClass' => $this->getCabinClass($itinerary),
            'layover' => $this->getLayoverInfo($scheduleDesc),
            'rawData' => $itinerary // Keep original data for booking
        ];
    }

    /**
     * Helper methods for formatting flight data
     */
    protected function getAirlineName(string $code): string
    {
        $airlines = [
            'EY' => 'Etihad Airways',
            'MS' => 'EgyptAir',
            'BG' => 'Biman Bangladesh Airlines',
            'KU' => 'Kuwait Airways',
            'EK' => 'Emirates',
            'CZ' => 'China Southern Airlines',
            'AI' => 'Air India',
            'QR' => 'Qatar Airways',
            'TK' => 'Turkish Airlines',
            'BS' => 'US-Bangla Airlines',
            'WY' => 'Oman Air',
            'GF' => 'Gulf Air',
            'UL' => 'SriLankan Airlines',
            'SV' => 'Saudi Arabian Airlines',
            'FZ' => 'flydubai',
            'H9' => 'Himalaya Airlines',
            'CA' => 'Air China',
            'H1' => 'Hahn Air',
            // Add more airlines as needed
        ];

        return $airlines[$code] ?? $code;
    }

    protected function getAirlineLogo(string $code): string
    {
        return "/images/airlines/" . strtolower($code) . ".png";
    }

    protected function formatTime(string $timeString): string
    {
        try {
            $time = Carbon::parse($timeString);
            return $time->format('H:i');
        } catch (\Exception $e) {
            return $timeString;
        }
    }

    protected function formatDate(string $timeString): string
    {
        try {
            $time = Carbon::parse($timeString);
            return $time->format('M d');
        } catch (\Exception $e) {
            return $timeString;
        }
    }

    protected function formatDuration(int $minutes): string
    {
        $hours = floor($minutes / 60);
        $mins = $minutes % 60;
        return sprintf("%dh %02dm", $hours, $mins);
    }

    protected function formatPrice(float|string $price): float
    {
        // If string, strip currency and convert to float
        if (is_string($price)) {
            $price = floatval(preg_replace('/[^\d.]/', '', $price));
        }

        return round($price, 2);
    }

    protected function extractCurrency(string $priceString): string
    {
        // Extract currency code from price string like "BDT31112"
        preg_match('/^[A-Z]{3}/', $priceString, $matches);
        return $matches[0] ?? 'BDT';
    }

    protected function getCabinClass(array $itinerary): string
    {
        // Default to Economy, can be enhanced based on actual data structure
        return 'Economy';
    }

    protected function getLayoverInfo(array $scheduleDesc): ?string
    {
        if ($scheduleDesc['stopCount'] > 0 && isset($scheduleDesc['hiddenStops'])) {
            $stops = $scheduleDesc['hiddenStops'];
            $layovers = [];
            foreach ($stops as $stop) {
                if (isset($stop['elapsedLayoverTime'])) {
                    $layovers[] = $stop['airport'] . ' (' . $this->formatDuration($stop['elapsedLayoverTime']) . ')';
                }
            }
            return implode(', ', $layovers);
        }
        return null;
    }

    /**
     * Test method to demonstrate flight list with sample data
     */
    public function testFlightList()
    {
        // Load sample data from the JSON file
        $jsonPath = public_path('flight_response.json');
        if (!file_exists($jsonPath)) {
            // Create sample data if file doesn't exist
            $sampleData = $this->createSampleFlightData();
        } else {
            $sampleData = json_decode(file_get_contents($jsonPath), true);
        }

        // Parse and format the sample data
        $formattedFlights = $this->parseFlightResponse($sampleData);

        return view("flight_list", compact('formattedFlights', 'sampleData'));
    }

    /**
     * Create sample flight data for testing
     */
    protected function createSampleFlightData(): array
    {
        return [
            'groupedItineraryResponse' => [
                'version' => '7.0.2',
                'statistics' => ['itineraryCount' => 3],
                'scheduleDescs' => [
                    [
                        'id' => 1,
                        'frequency' => 'SMTWTFS',
                        'stopCount' => 0,
                        'eTicketable' => true,
                        'totalMilesFlown' => 2202,
                        'elapsedTime' => 300,
                        'departure' => [
                            'airport' => 'DAC',
                            'city' => 'DAC',
                            'country' => 'BD',
                            'time' => '2025-01-20T19:30:00+06:00',
                            'terminal' => '1'
                        ],
                        'arrival' => [
                            'airport' => 'DXB',
                            'city' => 'DXB',
                            'country' => 'AE',
                            'time' => '2025-01-20T22:30:00+04:00',
                            'terminal' => '3'
                        ],
                        'carrier' => [
                            'marketing' => 'EK',
                            'marketingFlightNumber' => 587,
                            'operating' => 'EK',
                            'operatingFlightNumber' => 587
                        ]
                    ],
                    [
                        'id' => 2,
                        'frequency' => 'SMTWTFS',
                        'stopCount' => 1,
                        'eTicketable' => true,
                        'totalMilesFlown' => 2451,
                        'elapsedTime' => 410,
                        'departure' => [
                            'airport' => 'DAC',
                            'city' => 'DAC',
                            'country' => 'BD',
                            'time' => '2025-01-20T17:25:00+06:00'
                        ],
                        'arrival' => [
                            'airport' => 'DXB',
                            'city' => 'DXB',
                            'country' => 'AE',
                            'time' => '2025-01-20T22:15:00+04:00'
                        ],
                        'carrier' => [
                            'marketing' => 'BG',
                            'marketingFlightNumber' => 147,
                            'operating' => 'BG',
                            'operatingFlightNumber' => 147
                        ],
                        'hiddenStops' => [
                            [
                                'airport' => 'CGP',
                                'elapsedLayoverTime' => 60
                            ]
                        ]
                    ]
                ],
                'itineraryGroups' => [
                    [
                        'itineraries' => [
                            [
                                'id' => 'itin1',
                                'legs' => [['ref' => 1]],
                                'pricingInformation' => [
                                    [
                                        'fare' => [
                                            'totalFare' => 'BDT31112'
                                        ]
                                    ]
                                ]
                            ],
                            [
                                'id' => 'itin2',
                                'legs' => [['ref' => 2]],
                                'pricingInformation' => [
                                    [
                                        'fare' => [
                                            'totalFare' => 'BDT33715'
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    public function searchFlights_old(Request $request)
    {

        $request->validate([
            'origin' => 'required|string|size:3',
            'destination' => 'required|string|size:3',
            'departure_date' => 'required|date',
            'adults' => 'required|integer|min:1',
            'children' => 'nullable|integer|min:0',
            'infants' => 'nullable|integer|min:0',
            'trip_type' => 'required|in:one,round',
            //'return_date' => 'required_if:trip_type,round|date|after_or_equal:departure_date',
        ]);

        $origin = $request->input('origin');
        $destination = $request->input('destination');
        $departureDate = $request->input('departure_date');
        $returnDate = $request->input('return_date');

        $adults = $request->input('adults');
        $children = $request->input('children');
        $infants = $request->input('infants');
        $trip_type = $request->input('trip_type');

        $passengercount = $adults + $children + $infants;

        // Build PassengerTypeQuantity array
        $passengerTypeQuantity = [];
        if ($adults > 0) {
            $passengerTypeQuantity[] = [
                "Quantity" => $adults,
                "Code" => "ADT",
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }
        if ($children > 0) {
            $passengerTypeQuantity[] = [
                "Quantity" => $children,
                "Code" => "C03",
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }
        if ($infants > 0) {
            $passengerTypeQuantity[] = [
                "Quantity" => $infants,
                "Code" => "INF",
                "TPA_Extensions" => [
                    "VoluntaryChanges" => ["Match" => "Info"]
                ]
            ];
        }


        $saber_url = 'https://api.cert.platform.sabre.com/v5/offers/shop';

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('SABRE_Token'),
            'Content-Type' => 'application/json'
        ])->post($saber_url, [
            "OTA_AirLowFareSearchRQ" => [
                "Version" => "5",
                "POS" => [
                    "Source" => [
                        [
                            "PseudoCityCode" => env('SABRE_PCC'),
                            "RequestorID" => [
                                "Type" => "1",
                                "ID" => "1",
                                "CompanyName" => [
                                    "Code" => "TN",
                                    "content" => "TN"
                                ]
                            ]
                        ]
                    ]
                ],

                "AvailableFlightsOnly" => true,
                "OriginDestinationInformation" => array_values(array_filter([
                    [
                        "RPH" => "1",
                        "DepartureDateTime" => $departureDate,
                        "OriginLocation" => ["LocationCode" => $origin],
                        "DestinationLocation" => ["LocationCode" => $destination],
                        "TPA_Extensions" => (object)[] // empty for now
                    ],
                    $returnDate ? [
                        "RPH" => "2",
                        "DepartureDateTime" => $returnDate,
                        "OriginLocation" => ["LocationCode" => $destination],
                        "DestinationLocation" => ["LocationCode" => $origin],
                        "TPA_Extensions" => (object)[]
                    ] : null
                ])),

                "TravelPreferences" => [
                    "MaxStopsQuantity" => 4,
                    "TPA_Extensions" => [
                        "LongConnectTime" => [
                            "Enable" => true,
                            "Max" => 1439,
                            "Min" => 180
                        ],
                        "XOFares" => ["Value" => true],
                        "FlexibleFares" => [
                            "FareParameters" => [
                                [
                                    "RefundPenalty" => ["Ind" => true],
                                    "VoluntaryChanges" => ["Match" => "Info"]
                                ]
                            ]
                        ]
                    ],
                    "Baggage" => [
                        "RequestType" => "C",
                        "Description" => true
                    ]
                ],

                "TravelerInfoSummary" => [
                    "SeatsRequested" => [$passengercount],
                    "AirTravelerAvail" => [
                        [
                            "PassengerTypeQuantity" => $passengerTypeQuantity
                        ]
                    ]
                ],

                "TPA_Extensions" => [
                    "IntelliSellTransaction" => [
                        "RequestType" =>  ["Name" => "50ITINS"],
                    ]

                ]

            ]

        ]);



        return $response->json();
    }
}
