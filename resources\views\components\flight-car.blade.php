<div class="border rounded-xl shadow p-4 mb-6 bg-white">
    <div class="flex justify-between items-center">
        <div class="flex items-center gap-4">
            <img src="{{ $logoUrl ?? '/images/airline-default.png' }}" alt="Airline Logo" class="h-6 w-auto">
            <div>
                <p class="font-semibold">{{ $airlineName }} <span class="text-sm text-gray-500">{{ $flightNumber }}</span></p>
                <p class="text-sm text-gray-500">{{ $departureAirport }} - {{ $arrivalAirport }}</p>
            </div>
        </div>
        <div class="text-right">
            <p class="font-bold text-lg text-blue-600">BDT {{ number_format($price, 0) }}</p>
            <p class="text-sm text-gray-400 line-through">BDT {{ number_format($originalPrice, 0) }}</p>
        </div>
    </div>

    <div class="grid grid-cols-3 mt-4 gap-4 text-sm text-gray-700">
        <div>
            <p class="font-semibold">Departure</p>
            <p>{{ $departureTime }}<br>{{ $departureDate }}</p>
        </div>
        <div class="text-center">
            <p class="font-semibold">{{ $stops > 0 ? "$stops stop(s)" : "Non-stop" }}</p>
            <p class="text-xs">{{ $duration }}</p>
        </div>
        <div class="text-right">
            <p class="font-semibold">Arrival</p>
            <p>{{ $arrivalTime }}<br>{{ $arrivalDate }}</p>
        </div>
    </div>

    @if (!empty($layover))
        <div class="mt-2 text-xs text-gray-500">
            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Layover: {{ $layover }}</span>
        </div>
    @endif

    <div class="mt-4 flex justify-end">
        <button class="bg-yellow-500 text-white font-semibold px-6 py-2 rounded hover:bg-yellow-600">Select</button>
    </div>
</div>
